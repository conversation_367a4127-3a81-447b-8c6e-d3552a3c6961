import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Switch,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import {
  User,
  Settings,
  Volume2,
  Palette,
  MessageCircle,
  Heart,
  Shield,
  HelpCircle,
  ChevronRight,
  Bell,
  Moon,
  Smartphone
} from 'lucide-react-native';
import * as Haptics from 'expo-haptics';
import { Platform } from 'react-native';
import Colors from '@/constants/Colors';
import { useChatStore } from '@/stores/chatStore';
import { useAvatarStore } from '@/stores/avatarStore';

export default function ProfileScreen() {
  const router = useRouter();
  const { conversationContext, updatePreferences, speechConfig, updateSpeechConfig } = useChatStore();
  const { avatarConfig } = useAvatarStore();

  const handleSettingPress = (setting: string) => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    switch (setting) {
      case 'avatar-customization':
        router.push('/avatar-customization');
        break;
      case 'settings':
        router.push('/settings');
        break;
      default:
        console.log(`Navigate to ${setting}`);
    }
  };

  const toggleNotifications = (value: boolean) => {
    // TODO: Implement notifications toggle
    console.log('Notifications:', value);
  };

  const toggleDarkMode = (value: boolean) => {
    // TODO: Implement dark mode toggle
    console.log('Dark mode:', value);
  };

  const toggleHaptics = (value: boolean) => {
    // TODO: Implement haptics toggle
    console.log('Haptics:', value);
  };

  const renderSettingItem = (
    icon: React.ReactNode,
    title: string,
    subtitle?: string,
    onPress?: () => void,
    rightElement?: React.ReactNode,
    showChevron: boolean = true
  ) => (
    <TouchableOpacity
      style={styles.settingItem}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.settingIcon}>
        {icon}
      </View>
      <View style={styles.settingContent}>
        <Text style={styles.settingTitle}>{title}</Text>
        {subtitle && (
          <Text style={styles.settingSubtitle}>{subtitle}</Text>
        )}
      </View>
      {rightElement || (showChevron && (
        <ChevronRight size={20} color={Colors.textSecondary} />
      ))}
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <StatusBar style="light" />

      {/* Background Gradient */}
      <LinearGradient
        colors={[Colors.gradientStart, Colors.gradientMiddle, Colors.gradientEnd]}
        style={styles.background}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />

      <SafeAreaView style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Profile</Text>
          <Text style={styles.headerSubtitle}>
            Customize your experience
          </Text>
        </View>

        <ScrollView style={styles.scrollContent} showsVerticalScrollIndicator={false}>
          {/* User Info */}
          <View style={styles.userSection}>
            <View style={styles.userAvatar}>
              <User size={40} color={Colors.primary} />
            </View>
            <View style={styles.userInfo}>
              <Text style={styles.userName}>You</Text>
              <Text style={styles.userSubtitle}>
                Chatting with {conversationContext.personality.name}
              </Text>
            </View>
          </View>

          {/* Quick Stats */}
          <View style={styles.statsSection}>
            <View style={styles.statItem}>
              <MessageCircle size={24} color={Colors.primary} />
              <Text style={styles.statNumber}>127</Text>
              <Text style={styles.statLabel}>Messages</Text>
            </View>
            <View style={styles.statItem}>
              <Heart size={24} color={Colors.secondary} />
              <Text style={styles.statNumber}>7</Text>
              <Text style={styles.statLabel}>Days</Text>
            </View>
            <View style={styles.statItem}>
              <Settings size={24} color={Colors.info} />
              <Text style={styles.statNumber}>12</Text>
              <Text style={styles.statLabel}>Settings</Text>
            </View>
          </View>

          {/* Customization Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Customization</Text>
            <View style={styles.settingsList}>
              {renderSettingItem(
                <Palette size={24} color={Colors.secondary} />,
                'Avatar Customization',
                'Personalize your 3D companion',
                () => handleSettingPress('avatar-customization')
              )}
              {renderSettingItem(
                <Volume2 size={24} color={Colors.primary} />,
                'Voice Settings',
                `Speed: ${speechConfig.rate}x, Pitch: ${speechConfig.pitch}x`,
                () => handleSettingPress('voice-settings')
              )}
            </View>
          </View>

          {/* App Settings Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>App Settings</Text>
            <View style={styles.settingsList}>
              {renderSettingItem(
                <Bell size={24} color={Colors.warning} />,
                'Notifications',
                'Get notified about messages',
                undefined,
                <Switch
                  value={true}
                  onValueChange={toggleNotifications}
                  trackColor={{ false: Colors.border, true: Colors.primary }}
                  thumbColor={Colors.white}
                />,
                false
              )}
              {renderSettingItem(
                <Moon size={24} color={Colors.info} />,
                'Dark Mode',
                'Switch to dark theme',
                undefined,
                <Switch
                  value={false}
                  onValueChange={toggleDarkMode}
                  trackColor={{ false: Colors.border, true: Colors.primary }}
                  thumbColor={Colors.white}
                />,
                false
              )}
              {renderSettingItem(
                <Smartphone size={24} color={Colors.success} />,
                'Haptic Feedback',
                'Feel the interactions',
                undefined,
                <Switch
                  value={true}
                  onValueChange={toggleHaptics}
                  trackColor={{ false: Colors.border, true: Colors.primary }}
                  thumbColor={Colors.white}
                />,
                false
              )}
            </View>
          </View>

          {/* Support Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Support</Text>
            <View style={styles.settingsList}>
              {renderSettingItem(
                <HelpCircle size={24} color={Colors.info} />,
                'Help & FAQ',
                'Get answers to common questions',
                () => handleSettingPress('help')
              )}
              {renderSettingItem(
                <Shield size={24} color={Colors.success} />,
                'Privacy Policy',
                'Learn about data protection',
                () => handleSettingPress('privacy')
              )}
              {renderSettingItem(
                <MessageCircle size={24} color={Colors.primary} />,
                'Contact Support',
                'Get help from our team',
                () => handleSettingPress('support')
              )}
            </View>
          </View>

          {/* App Info */}
          <View style={styles.appInfo}>
            <Text style={styles.appInfoText}>
              Replika 3D Avatar v1.0.0
            </Text>
            <Text style={styles.appInfoSubtext}>
              Made with ❤️ for meaningful connections
            </Text>
          </View>
        </ScrollView>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  background: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  content: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: Colors.textSecondary,
    opacity: 0.9,
  },
  scrollContent: {
    flex: 1,
  },
  userSection: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.backgroundCard + '80',
    marginHorizontal: 20,
    padding: 20,
    borderRadius: 15,
    marginBottom: 20,
  },
  userAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 4,
  },
  userSubtitle: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  statsSection: {
    flexDirection: 'row',
    backgroundColor: Colors.backgroundCard + '60',
    marginHorizontal: 20,
    borderRadius: 15,
    padding: 16,
    marginBottom: 30,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.text,
    marginVertical: 8,
  },
  statLabel: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 16,
    paddingHorizontal: 20,
  },
  settingsList: {
    backgroundColor: Colors.backgroundCard + '60',
    marginHorizontal: 20,
    borderRadius: 15,
    overflow: 'hidden',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border + '30',
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.backgroundCard,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  appInfo: {
    alignItems: 'center',
    paddingVertical: 30,
    paddingHorizontal: 20,
  },
  appInfoText: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 4,
  },
  appInfoSubtext: {
    fontSize: 12,
    color: Colors.textSecondary,
    opacity: 0.7,
  },
});

export default ProfileScreen;
