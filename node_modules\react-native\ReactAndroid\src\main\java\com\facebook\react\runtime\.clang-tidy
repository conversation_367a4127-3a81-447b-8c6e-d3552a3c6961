---
# NOTE there must be no spaces before the '-', so put the comma last.
InheritParentConfig: true
Checks: '
-,
-cert-err60-cpp,
-cppcoreguidelines-pro-bounds-pointer-arithmetic,
-cppcoreguidelines-special-member-functions,
-cppcoreguidelines-pro-type-const-cast,
-fuchsia-default-arguments-calls,
-fuchsia-multiple-inheritance,
-google-readability-casting,
-google-runtime-int,
-google-runtime-references,
-hicpp-special-member-functions,
-llvm-header-guard,
-misc-non-private-member-variables-in-classes,
-misc-unused-parameters,
-modernize-use-trailing-return-type,
-performance-unnecessary-value-param,
'
...
