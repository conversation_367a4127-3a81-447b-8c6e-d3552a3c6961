{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "getVisitor", "_assert", "require", "_hoist", "_emit", "_replaceShorthandObjectMethod", "util", "t", "Method", "path", "state", "node", "shouldRegenerate", "container", "functionExpression", "cloneNode", "body", "generator", "async", "get", "set", "returnStatement", "callExpression", "unwrapFunctionEnvironment", "Function", "exit", "wrapWithTypes", "replaceShorthandObjectMethod", "contextId", "scope", "generateUidIdentifier", "argsId", "ensureBlock", "body<PERSON>lockPath", "traverse", "await<PERSON><PERSON>tor", "functionSentVisitor", "context", "outerBody", "innerBody", "for<PERSON>ach", "child<PERSON><PERSON>", "isExpressionStatement", "isStringLiteral", "expression", "push", "_blockHoist", "length", "outerFnExpr", "getOuterFnExpr", "assertIdentifier", "id", "innerFnId", "identifier", "name", "vars", "hoist", "usesThis", "usesArguments", "getArgsId", "clone", "argumentsThisVisitor", "variableDeclaration", "declarations", "variableDeclarator", "emitter", "Emitter", "explode", "wrapArgs", "getContextFunction", "tryLocsList", "getTryLocsList", "nullLiteral", "thisExpression", "currentScope", "hasOwnBinding", "rename", "parent", "wrapCall", "runtimeProperty", "blockStatement", "p", "registerDeclaration", "oldDirectives", "directives", "wasGeneratorFunction", "isExpression", "replaceWithOrRemove", "addComment", "insertedLocs", "getInsertedLocs", "NumericLiteral", "has", "replaceWith", "numericLiteral", "requeue", "opts", "asyncGenerators", "generators", "funPath", "getTypes", "assertFunction", "isFunctionDeclaration", "getMarkedFunctionId", "markInfo", "WeakMap", "getMarkInfo", "blockPath", "findParent", "isProgram", "isBlockStatement", "block", "assert", "ok", "Array", "isArray", "info", "decl", "unshiftContainer", "decl<PERSON>ath", "strictEqual", "markedId", "markCallExp", "index", "markCallExpPath", "FunctionExpression|FunctionDeclaration|Method", "skip", "Identifier", "isReference", "ThisExpression", "MetaProperty", "meta", "property", "memberExpression", "AwaitExpression", "argument", "yieldExpression"], "sources": ["../../src/regenerator/visit.ts"], "sourcesContent": ["\"use strict\";\n\nimport assert from \"node:assert\";\nimport { hoist } from \"./hoist.ts\";\nimport { Emitter } from \"./emit.ts\";\nimport replaceShorthandObjectMethod from \"./replaceShorthandObjectMethod.ts\";\nimport * as util from \"./util.ts\";\n\nexport const getVisitor = (t: any) => ({\n  Method(path: any, state: any) {\n    const node = path.node;\n\n    if (!shouldRegenerate(node, state)) return;\n\n    const container = t.functionExpression(\n      null,\n      [],\n      t.cloneNode(node.body, false),\n      node.generator,\n      node.async,\n    );\n\n    path\n      .get(\"body\")\n      .set(\"body\", [t.returnStatement(t.callExpression(container, []))]);\n\n    // Regardless of whether or not the wrapped function is a an async method\n    // or generator the outer function should not be\n    node.async = false;\n    node.generator = false;\n\n    // Unwrap the wrapper IIFE's environment so super and this and such still work.\n    path.get(\"body.body.0.argument.callee\").unwrapFunctionEnvironment();\n  },\n  Function: {\n    exit: util.wrapWithTypes(t, function (path: any, state: any) {\n      let node = path.node;\n\n      if (!shouldRegenerate(node, state)) return;\n\n      // if this is an ObjectMethod, we need to convert it to an ObjectProperty\n      path = replaceShorthandObjectMethod(path);\n      node = path.node;\n\n      const contextId = path.scope.generateUidIdentifier(\"context\");\n      const argsId = path.scope.generateUidIdentifier(\"args\");\n\n      path.ensureBlock();\n      const bodyBlockPath = path.get(\"body\");\n\n      if (node.async) {\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        bodyBlockPath.traverse(awaitVisitor);\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      bodyBlockPath.traverse(functionSentVisitor, {\n        context: contextId,\n      });\n\n      const outerBody: any[] = [];\n      const innerBody: any[] = [];\n\n      bodyBlockPath.get(\"body\").forEach(function (childPath: any) {\n        const node = childPath.node;\n        if (\n          t.isExpressionStatement(node) &&\n          t.isStringLiteral(node.expression)\n        ) {\n          // Babylon represents directives like \"use strict\" as elements\n          // of a bodyBlockPath.node.directives array, but they could just\n          // as easily be represented (by other parsers) as traditional\n          // string-literal-valued expression statements, so we need to\n          // handle that here. (#248)\n          outerBody.push(node);\n        } else if (node?._blockHoist != null) {\n          outerBody.push(node);\n        } else {\n          innerBody.push(node);\n        }\n      });\n\n      if (outerBody.length > 0) {\n        // Only replace the inner body if we actually hoisted any statements\n        // to the outer body.\n        bodyBlockPath.node.body = innerBody;\n      }\n\n      const outerFnExpr = getOuterFnExpr(path);\n      // Note that getOuterFnExpr has the side-effect of ensuring that the\n      // function has a name (so node.id will always be an Identifier), even\n      // if a temporary name has to be synthesized.\n      t.assertIdentifier(node.id);\n      const innerFnId = t.identifier(node.id.name + \"$\");\n\n      // Turn all declarations into vars, and replace the original\n      // declarations with equivalent assignment expressions.\n      let vars = hoist(path);\n\n      const context = {\n        usesThis: false,\n        usesArguments: false,\n        getArgsId: () => t.clone(argsId),\n      };\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      path.traverse(argumentsThisVisitor, context);\n\n      if (context.usesArguments) {\n        vars = vars || t.variableDeclaration(\"var\", []);\n        vars.declarations.push(\n          t.variableDeclarator(t.clone(argsId), t.identifier(\"arguments\")),\n        );\n      }\n\n      const emitter = new (Emitter as any)(contextId);\n      emitter.explode(path.get(\"body\"));\n\n      if (vars && vars.declarations.length > 0) {\n        outerBody.push(vars);\n      }\n\n      const wrapArgs = [emitter.getContextFunction(innerFnId)];\n      const tryLocsList = emitter.getTryLocsList();\n\n      if (node.generator) {\n        wrapArgs.push(outerFnExpr);\n      } else if (context.usesThis || tryLocsList || node.async) {\n        // Async functions that are not generators don't care about the\n        // outer function because they don't need it to be marked and don't\n        // inherit from its .prototype.\n        wrapArgs.push(t.nullLiteral());\n      }\n      if (context.usesThis) {\n        wrapArgs.push(t.thisExpression());\n      } else if (tryLocsList || node.async) {\n        wrapArgs.push(t.nullLiteral());\n      }\n      if (tryLocsList) {\n        wrapArgs.push(tryLocsList);\n      } else if (node.async) {\n        wrapArgs.push(t.nullLiteral());\n      }\n\n      if (node.async) {\n        // Rename any locally declared \"Promise\" variable,\n        // to use the global one.\n        let currentScope = path.scope;\n        do {\n          if (currentScope.hasOwnBinding(\"Promise\"))\n            currentScope.rename(\"Promise\");\n        } while ((currentScope = currentScope.parent));\n\n        wrapArgs.push(t.identifier(\"Promise\"));\n      }\n\n      const wrapCall = t.callExpression(\n        util.runtimeProperty(node.async ? \"async\" : \"wrap\"),\n        wrapArgs,\n      );\n\n      outerBody.push(t.returnStatement(wrapCall));\n      node.body = t.blockStatement(outerBody);\n      // We injected a few new variable declarations (for every hoisted var),\n      // so we need to add them to the scope.\n      path.get(\"body.body\").forEach((p: any) => p.scope.registerDeclaration(p));\n\n      const oldDirectives = bodyBlockPath.node.directives;\n      if (oldDirectives) {\n        // Babylon represents directives like \"use strict\" as elements of\n        // a bodyBlockPath.node.directives array. (#248)\n        node.body.directives = oldDirectives;\n      }\n\n      const wasGeneratorFunction = node.generator;\n      if (wasGeneratorFunction) {\n        node.generator = false;\n      }\n\n      if (node.async) {\n        node.async = false;\n      }\n\n      if (wasGeneratorFunction && t.isExpression(node)) {\n        util.replaceWithOrRemove(\n          path,\n          t.callExpression(util.runtimeProperty(\"mark\"), [node]),\n        );\n        path.addComment(\"leading\", \"#__PURE__\");\n      }\n\n      const insertedLocs = emitter.getInsertedLocs();\n\n      path.traverse({\n        NumericLiteral(path: any) {\n          if (!insertedLocs.has(path.node)) {\n            return;\n          }\n\n          path.replaceWith(t.numericLiteral(path.node.value));\n        },\n      });\n\n      // Generators are processed in 'exit' handlers so that regenerator only has to run on\n      // an ES5 AST, but that means traversal will not pick up newly inserted references\n      // to things like 'regeneratorRuntime'. To avoid this, we explicitly requeue.\n      path.requeue();\n    }),\n  },\n});\n\n// Check if a node should be transformed by regenerator\nfunction shouldRegenerate(node: any, state: any) {\n  if (node.generator) {\n    if (node.async) {\n      // Async generator\n      return state.opts.asyncGenerators !== false;\n    } else {\n      // Plain generator\n      return state.opts.generators !== false;\n    }\n  } else if (node.async) {\n    // Async function\n    return state.opts.async !== false;\n  } else {\n    // Not a generator or async function.\n    return false;\n  }\n}\n\n// Given a NodePath for a Function, return an Expression node that can be\n// used to refer reliably to the function object from inside the function.\n// This expression is essentially a replacement for arguments.callee, with\n// the key advantage that it works in strict mode.\nfunction getOuterFnExpr(funPath: any) {\n  const t = util.getTypes();\n  const node = funPath.node;\n  t.assertFunction(node);\n\n  if (!node.id) {\n    // Default-exported function declarations, and function expressions may not\n    // have a name to reference, so we explicitly add one.\n    node.id = funPath.scope.parent.generateUidIdentifier(\"callee\");\n  }\n\n  if (\n    node.generator && // Non-generator functions don't need to be marked.\n    t.isFunctionDeclaration(node)\n  ) {\n    // Return the identifier returned by runtime.mark(<node.id>).\n    return getMarkedFunctionId(funPath);\n  }\n\n  return t.clone(node.id);\n}\n\nconst markInfo = new WeakMap();\n\nfunction getMarkInfo(node: any) {\n  if (!markInfo.has(node)) {\n    markInfo.set(node, {});\n  }\n  return markInfo.get(node);\n}\n\nfunction getMarkedFunctionId(funPath: any) {\n  const t = util.getTypes();\n  const node = funPath.node;\n  t.assertIdentifier(node.id);\n\n  const blockPath = funPath.findParent(function (path: any) {\n    return path.isProgram() || path.isBlockStatement();\n  });\n\n  if (!blockPath) {\n    return node.id;\n  }\n\n  const block = blockPath.node;\n  assert.ok(Array.isArray(block.body));\n\n  const info = getMarkInfo(block);\n  if (!info.decl) {\n    info.decl = t.variableDeclaration(\"var\", []);\n    blockPath.unshiftContainer(\"body\", info.decl);\n    info.declPath = blockPath.get(\"body.0\");\n  }\n\n  assert.strictEqual(info.declPath.node, info.decl);\n\n  // Get a new unique identifier for our marked variable.\n  const markedId = blockPath.scope.generateUidIdentifier(\"marked\");\n  const markCallExp = t.callExpression(util.runtimeProperty(\"mark\"), [\n    t.clone(node.id),\n  ]);\n\n  const index =\n    info.decl.declarations.push(t.variableDeclarator(markedId, markCallExp)) -\n    1;\n\n  const markCallExpPath = info.declPath.get(\"declarations.\" + index + \".init\");\n\n  assert.strictEqual(markCallExpPath.node, markCallExp);\n\n  markCallExpPath.addComment(\"leading\", \"#__PURE__\");\n\n  return t.clone(markedId);\n}\n\nconst argumentsThisVisitor = {\n  \"FunctionExpression|FunctionDeclaration|Method\": function (path: any) {\n    path.skip();\n  },\n\n  Identifier: function (path: any, state: any) {\n    if (path.node.name === \"arguments\" && util.isReference(path)) {\n      util.replaceWithOrRemove(path, state.getArgsId());\n      state.usesArguments = true;\n    }\n  },\n\n  ThisExpression: function (path: any, state: any) {\n    state.usesThis = true;\n  },\n};\n\nconst functionSentVisitor = {\n  MetaProperty(path: any) {\n    const { node } = path;\n\n    if (node.meta.name === \"function\" && node.property.name === \"sent\") {\n      const t = util.getTypes();\n      util.replaceWithOrRemove(\n        path,\n        t.memberExpression(\n          t.clone((this as any).context),\n          t.identifier(\"_sent\"),\n        ),\n      );\n    }\n  },\n};\n\nconst awaitVisitor = {\n  Function: function (path: any) {\n    path.skip(); // Don't descend into nested function scopes.\n  },\n\n  AwaitExpression: function (path: any) {\n    const t = util.getTypes();\n\n    // Convert await expressions to yield expressions.\n    const argument = path.node.argument;\n\n    // Transforming `await x` to `yield regeneratorRuntime.awrap(x)`\n    // causes the argument to be wrapped in such a way that the runtime\n    // can distinguish between awaited and merely yielded values.\n    util.replaceWithOrRemove(\n      path,\n      t.yieldExpression(\n        t.callExpression(util.runtimeProperty(\"awrap\"), [argument]),\n        false,\n      ),\n    );\n  },\n};\n"], "mappings": "AAAA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,UAAA;AAEb,IAAAC,OAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,6BAAA,GAAAH,OAAA;AACA,IAAAI,IAAA,GAAAJ,OAAA;AAEO,MAAMF,UAAU,GAAIO,CAAM,KAAM;EACrCC,MAAMA,CAACC,IAAS,EAAEC,KAAU,EAAE;IAC5B,MAAMC,IAAI,GAAGF,IAAI,CAACE,IAAI;IAEtB,IAAI,CAACC,gBAAgB,CAACD,IAAI,EAAED,KAAK,CAAC,EAAE;IAEpC,MAAMG,SAAS,GAAGN,CAAC,CAACO,kBAAkB,CACpC,IAAI,EACJ,EAAE,EACFP,CAAC,CAACQ,SAAS,CAACJ,IAAI,CAACK,IAAI,EAAE,KAAK,CAAC,EAC7BL,IAAI,CAACM,SAAS,EACdN,IAAI,CAACO,KACP,CAAC;IAEDT,IAAI,CACDU,GAAG,CAAC,MAAM,CAAC,CACXC,GAAG,CAAC,MAAM,EAAE,CAACb,CAAC,CAACc,eAAe,CAACd,CAAC,CAACe,cAAc,CAACT,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IAIpEF,IAAI,CAACO,KAAK,GAAG,KAAK;IAClBP,IAAI,CAACM,SAAS,GAAG,KAAK;IAGtBR,IAAI,CAACU,GAAG,CAAC,6BAA6B,CAAC,CAACI,yBAAyB,CAAC,CAAC;EACrE,CAAC;EACDC,QAAQ,EAAE;IACRC,IAAI,EAAEnB,IAAI,CAACoB,aAAa,CAACnB,CAAC,EAAE,UAAUE,IAAS,EAAEC,KAAU,EAAE;MAC3D,IAAIC,IAAI,GAAGF,IAAI,CAACE,IAAI;MAEpB,IAAI,CAACC,gBAAgB,CAACD,IAAI,EAAED,KAAK,CAAC,EAAE;MAGpCD,IAAI,GAAG,IAAAkB,qCAA4B,EAAClB,IAAI,CAAC;MACzCE,IAAI,GAAGF,IAAI,CAACE,IAAI;MAEhB,MAAMiB,SAAS,GAAGnB,IAAI,CAACoB,KAAK,CAACC,qBAAqB,CAAC,SAAS,CAAC;MAC7D,MAAMC,MAAM,GAAGtB,IAAI,CAACoB,KAAK,CAACC,qBAAqB,CAAC,MAAM,CAAC;MAEvDrB,IAAI,CAACuB,WAAW,CAAC,CAAC;MAClB,MAAMC,aAAa,GAAGxB,IAAI,CAACU,GAAG,CAAC,MAAM,CAAC;MAEtC,IAAIR,IAAI,CAACO,KAAK,EAAE;QAEde,aAAa,CAACC,QAAQ,CAACC,YAAY,CAAC;MACtC;MAGAF,aAAa,CAACC,QAAQ,CAACE,mBAAmB,EAAE;QAC1CC,OAAO,EAAET;MACX,CAAC,CAAC;MAEF,MAAMU,SAAgB,GAAG,EAAE;MAC3B,MAAMC,SAAgB,GAAG,EAAE;MAE3BN,aAAa,CAACd,GAAG,CAAC,MAAM,CAAC,CAACqB,OAAO,CAAC,UAAUC,SAAc,EAAE;QAC1D,MAAM9B,IAAI,GAAG8B,SAAS,CAAC9B,IAAI;QAC3B,IACEJ,CAAC,CAACmC,qBAAqB,CAAC/B,IAAI,CAAC,IAC7BJ,CAAC,CAACoC,eAAe,CAAChC,IAAI,CAACiC,UAAU,CAAC,EAClC;UAMAN,SAAS,CAACO,IAAI,CAAClC,IAAI,CAAC;QACtB,CAAC,MAAM,IAAI,CAAAA,IAAI,oBAAJA,IAAI,CAAEmC,WAAW,KAAI,IAAI,EAAE;UACpCR,SAAS,CAACO,IAAI,CAAClC,IAAI,CAAC;QACtB,CAAC,MAAM;UACL4B,SAAS,CAACM,IAAI,CAAClC,IAAI,CAAC;QACtB;MACF,CAAC,CAAC;MAEF,IAAI2B,SAAS,CAACS,MAAM,GAAG,CAAC,EAAE;QAGxBd,aAAa,CAACtB,IAAI,CAACK,IAAI,GAAGuB,SAAS;MACrC;MAEA,MAAMS,WAAW,GAAGC,cAAc,CAACxC,IAAI,CAAC;MAIxCF,CAAC,CAAC2C,gBAAgB,CAACvC,IAAI,CAACwC,EAAE,CAAC;MAC3B,MAAMC,SAAS,GAAG7C,CAAC,CAAC8C,UAAU,CAAC1C,IAAI,CAACwC,EAAE,CAACG,IAAI,GAAG,GAAG,CAAC;MAIlD,IAAIC,IAAI,GAAG,IAAAC,YAAK,EAAC/C,IAAI,CAAC;MAEtB,MAAM4B,OAAO,GAAG;QACdoB,QAAQ,EAAE,KAAK;QACfC,aAAa,EAAE,KAAK;QACpBC,SAAS,EAAEA,CAAA,KAAMpD,CAAC,CAACqD,KAAK,CAAC7B,MAAM;MACjC,CAAC;MAEDtB,IAAI,CAACyB,QAAQ,CAAC2B,oBAAoB,EAAExB,OAAO,CAAC;MAE5C,IAAIA,OAAO,CAACqB,aAAa,EAAE;QACzBH,IAAI,GAAGA,IAAI,IAAIhD,CAAC,CAACuD,mBAAmB,CAAC,KAAK,EAAE,EAAE,CAAC;QAC/CP,IAAI,CAACQ,YAAY,CAAClB,IAAI,CACpBtC,CAAC,CAACyD,kBAAkB,CAACzD,CAAC,CAACqD,KAAK,CAAC7B,MAAM,CAAC,EAAExB,CAAC,CAAC8C,UAAU,CAAC,WAAW,CAAC,CACjE,CAAC;MACH;MAEA,MAAMY,OAAO,GAAG,IAAKC,aAAO,CAAStC,SAAS,CAAC;MAC/CqC,OAAO,CAACE,OAAO,CAAC1D,IAAI,CAACU,GAAG,CAAC,MAAM,CAAC,CAAC;MAEjC,IAAIoC,IAAI,IAAIA,IAAI,CAACQ,YAAY,CAAChB,MAAM,GAAG,CAAC,EAAE;QACxCT,SAAS,CAACO,IAAI,CAACU,IAAI,CAAC;MACtB;MAEA,MAAMa,QAAQ,GAAG,CAACH,OAAO,CAACI,kBAAkB,CAACjB,SAAS,CAAC,CAAC;MACxD,MAAMkB,WAAW,GAAGL,OAAO,CAACM,cAAc,CAAC,CAAC;MAE5C,IAAI5D,IAAI,CAACM,SAAS,EAAE;QAClBmD,QAAQ,CAACvB,IAAI,CAACG,WAAW,CAAC;MAC5B,CAAC,MAAM,IAAIX,OAAO,CAACoB,QAAQ,IAAIa,WAAW,IAAI3D,IAAI,CAACO,KAAK,EAAE;QAIxDkD,QAAQ,CAACvB,IAAI,CAACtC,CAAC,CAACiE,WAAW,CAAC,CAAC,CAAC;MAChC;MACA,IAAInC,OAAO,CAACoB,QAAQ,EAAE;QACpBW,QAAQ,CAACvB,IAAI,CAACtC,CAAC,CAACkE,cAAc,CAAC,CAAC,CAAC;MACnC,CAAC,MAAM,IAAIH,WAAW,IAAI3D,IAAI,CAACO,KAAK,EAAE;QACpCkD,QAAQ,CAACvB,IAAI,CAACtC,CAAC,CAACiE,WAAW,CAAC,CAAC,CAAC;MAChC;MACA,IAAIF,WAAW,EAAE;QACfF,QAAQ,CAACvB,IAAI,CAACyB,WAAW,CAAC;MAC5B,CAAC,MAAM,IAAI3D,IAAI,CAACO,KAAK,EAAE;QACrBkD,QAAQ,CAACvB,IAAI,CAACtC,CAAC,CAACiE,WAAW,CAAC,CAAC,CAAC;MAChC;MAEA,IAAI7D,IAAI,CAACO,KAAK,EAAE;QAGd,IAAIwD,YAAY,GAAGjE,IAAI,CAACoB,KAAK;QAC7B,GAAG;UACD,IAAI6C,YAAY,CAACC,aAAa,CAAC,SAAS,CAAC,EACvCD,YAAY,CAACE,MAAM,CAAC,SAAS,CAAC;QAClC,CAAC,QAASF,YAAY,GAAGA,YAAY,CAACG,MAAM;QAE5CT,QAAQ,CAACvB,IAAI,CAACtC,CAAC,CAAC8C,UAAU,CAAC,SAAS,CAAC,CAAC;MACxC;MAEA,MAAMyB,QAAQ,GAAGvE,CAAC,CAACe,cAAc,CAC/BhB,IAAI,CAACyE,eAAe,CAACpE,IAAI,CAACO,KAAK,GAAG,OAAO,GAAG,MAAM,CAAC,EACnDkD,QACF,CAAC;MAED9B,SAAS,CAACO,IAAI,CAACtC,CAAC,CAACc,eAAe,CAACyD,QAAQ,CAAC,CAAC;MAC3CnE,IAAI,CAACK,IAAI,GAAGT,CAAC,CAACyE,cAAc,CAAC1C,SAAS,CAAC;MAGvC7B,IAAI,CAACU,GAAG,CAAC,WAAW,CAAC,CAACqB,OAAO,CAAEyC,CAAM,IAAKA,CAAC,CAACpD,KAAK,CAACqD,mBAAmB,CAACD,CAAC,CAAC,CAAC;MAEzE,MAAME,aAAa,GAAGlD,aAAa,CAACtB,IAAI,CAACyE,UAAU;MACnD,IAAID,aAAa,EAAE;QAGjBxE,IAAI,CAACK,IAAI,CAACoE,UAAU,GAAGD,aAAa;MACtC;MAEA,MAAME,oBAAoB,GAAG1E,IAAI,CAACM,SAAS;MAC3C,IAAIoE,oBAAoB,EAAE;QACxB1E,IAAI,CAACM,SAAS,GAAG,KAAK;MACxB;MAEA,IAAIN,IAAI,CAACO,KAAK,EAAE;QACdP,IAAI,CAACO,KAAK,GAAG,KAAK;MACpB;MAEA,IAAImE,oBAAoB,IAAI9E,CAAC,CAAC+E,YAAY,CAAC3E,IAAI,CAAC,EAAE;QAChDL,IAAI,CAACiF,mBAAmB,CACtB9E,IAAI,EACJF,CAAC,CAACe,cAAc,CAAChB,IAAI,CAACyE,eAAe,CAAC,MAAM,CAAC,EAAE,CAACpE,IAAI,CAAC,CACvD,CAAC;QACDF,IAAI,CAAC+E,UAAU,CAAC,SAAS,EAAE,WAAW,CAAC;MACzC;MAEA,MAAMC,YAAY,GAAGxB,OAAO,CAACyB,eAAe,CAAC,CAAC;MAE9CjF,IAAI,CAACyB,QAAQ,CAAC;QACZyD,cAAcA,CAAClF,IAAS,EAAE;UACxB,IAAI,CAACgF,YAAY,CAACG,GAAG,CAACnF,IAAI,CAACE,IAAI,CAAC,EAAE;YAChC;UACF;UAEAF,IAAI,CAACoF,WAAW,CAACtF,CAAC,CAACuF,cAAc,CAACrF,IAAI,CAACE,IAAI,CAACZ,KAAK,CAAC,CAAC;QACrD;MACF,CAAC,CAAC;MAKFU,IAAI,CAACsF,OAAO,CAAC,CAAC;IAChB,CAAC;EACH;AACF,CAAC,CAAC;AAACjG,OAAA,CAAAE,UAAA,GAAAA,UAAA;AAGH,SAASY,gBAAgBA,CAACD,IAAS,EAAED,KAAU,EAAE;EAC/C,IAAIC,IAAI,CAACM,SAAS,EAAE;IAClB,IAAIN,IAAI,CAACO,KAAK,EAAE;MAEd,OAAOR,KAAK,CAACsF,IAAI,CAACC,eAAe,KAAK,KAAK;IAC7C,CAAC,MAAM;MAEL,OAAOvF,KAAK,CAACsF,IAAI,CAACE,UAAU,KAAK,KAAK;IACxC;EACF,CAAC,MAAM,IAAIvF,IAAI,CAACO,KAAK,EAAE;IAErB,OAAOR,KAAK,CAACsF,IAAI,CAAC9E,KAAK,KAAK,KAAK;EACnC,CAAC,MAAM;IAEL,OAAO,KAAK;EACd;AACF;AAMA,SAAS+B,cAAcA,CAACkD,OAAY,EAAE;EACpC,MAAM5F,CAAC,GAAGD,IAAI,CAAC8F,QAAQ,CAAC,CAAC;EACzB,MAAMzF,IAAI,GAAGwF,OAAO,CAACxF,IAAI;EACzBJ,CAAC,CAAC8F,cAAc,CAAC1F,IAAI,CAAC;EAEtB,IAAI,CAACA,IAAI,CAACwC,EAAE,EAAE;IAGZxC,IAAI,CAACwC,EAAE,GAAGgD,OAAO,CAACtE,KAAK,CAACgD,MAAM,CAAC/C,qBAAqB,CAAC,QAAQ,CAAC;EAChE;EAEA,IACEnB,IAAI,CAACM,SAAS,IACdV,CAAC,CAAC+F,qBAAqB,CAAC3F,IAAI,CAAC,EAC7B;IAEA,OAAO4F,mBAAmB,CAACJ,OAAO,CAAC;EACrC;EAEA,OAAO5F,CAAC,CAACqD,KAAK,CAACjD,IAAI,CAACwC,EAAE,CAAC;AACzB;AAEA,MAAMqD,QAAQ,GAAG,IAAIC,OAAO,CAAC,CAAC;AAE9B,SAASC,WAAWA,CAAC/F,IAAS,EAAE;EAC9B,IAAI,CAAC6F,QAAQ,CAACZ,GAAG,CAACjF,IAAI,CAAC,EAAE;IACvB6F,QAAQ,CAACpF,GAAG,CAACT,IAAI,EAAE,CAAC,CAAC,CAAC;EACxB;EACA,OAAO6F,QAAQ,CAACrF,GAAG,CAACR,IAAI,CAAC;AAC3B;AAEA,SAAS4F,mBAAmBA,CAACJ,OAAY,EAAE;EACzC,MAAM5F,CAAC,GAAGD,IAAI,CAAC8F,QAAQ,CAAC,CAAC;EACzB,MAAMzF,IAAI,GAAGwF,OAAO,CAACxF,IAAI;EACzBJ,CAAC,CAAC2C,gBAAgB,CAACvC,IAAI,CAACwC,EAAE,CAAC;EAE3B,MAAMwD,SAAS,GAAGR,OAAO,CAACS,UAAU,CAAC,UAAUnG,IAAS,EAAE;IACxD,OAAOA,IAAI,CAACoG,SAAS,CAAC,CAAC,IAAIpG,IAAI,CAACqG,gBAAgB,CAAC,CAAC;EACpD,CAAC,CAAC;EAEF,IAAI,CAACH,SAAS,EAAE;IACd,OAAOhG,IAAI,CAACwC,EAAE;EAChB;EAEA,MAAM4D,KAAK,GAAGJ,SAAS,CAAChG,IAAI;EAC5BqG,OAAM,CAACC,EAAE,CAACC,KAAK,CAACC,OAAO,CAACJ,KAAK,CAAC/F,IAAI,CAAC,CAAC;EAEpC,MAAMoG,IAAI,GAAGV,WAAW,CAACK,KAAK,CAAC;EAC/B,IAAI,CAACK,IAAI,CAACC,IAAI,EAAE;IACdD,IAAI,CAACC,IAAI,GAAG9G,CAAC,CAACuD,mBAAmB,CAAC,KAAK,EAAE,EAAE,CAAC;IAC5C6C,SAAS,CAACW,gBAAgB,CAAC,MAAM,EAAEF,IAAI,CAACC,IAAI,CAAC;IAC7CD,IAAI,CAACG,QAAQ,GAAGZ,SAAS,CAACxF,GAAG,CAAC,QAAQ,CAAC;EACzC;EAEA6F,OAAM,CAACQ,WAAW,CAACJ,IAAI,CAACG,QAAQ,CAAC5G,IAAI,EAAEyG,IAAI,CAACC,IAAI,CAAC;EAGjD,MAAMI,QAAQ,GAAGd,SAAS,CAAC9E,KAAK,CAACC,qBAAqB,CAAC,QAAQ,CAAC;EAChE,MAAM4F,WAAW,GAAGnH,CAAC,CAACe,cAAc,CAAChB,IAAI,CAACyE,eAAe,CAAC,MAAM,CAAC,EAAE,CACjExE,CAAC,CAACqD,KAAK,CAACjD,IAAI,CAACwC,EAAE,CAAC,CACjB,CAAC;EAEF,MAAMwE,KAAK,GACTP,IAAI,CAACC,IAAI,CAACtD,YAAY,CAAClB,IAAI,CAACtC,CAAC,CAACyD,kBAAkB,CAACyD,QAAQ,EAAEC,WAAW,CAAC,CAAC,GACxE,CAAC;EAEH,MAAME,eAAe,GAAGR,IAAI,CAACG,QAAQ,CAACpG,GAAG,CAAC,eAAe,GAAGwG,KAAK,GAAG,OAAO,CAAC;EAE5EX,OAAM,CAACQ,WAAW,CAACI,eAAe,CAACjH,IAAI,EAAE+G,WAAW,CAAC;EAErDE,eAAe,CAACpC,UAAU,CAAC,SAAS,EAAE,WAAW,CAAC;EAElD,OAAOjF,CAAC,CAACqD,KAAK,CAAC6D,QAAQ,CAAC;AAC1B;AAEA,MAAM5D,oBAAoB,GAAG;EAC3B,+CAA+C,EAAE,SAAAgE,CAAUpH,IAAS,EAAE;IACpEA,IAAI,CAACqH,IAAI,CAAC,CAAC;EACb,CAAC;EAEDC,UAAU,EAAE,SAAAA,CAAUtH,IAAS,EAAEC,KAAU,EAAE;IAC3C,IAAID,IAAI,CAACE,IAAI,CAAC2C,IAAI,KAAK,WAAW,IAAIhD,IAAI,CAAC0H,WAAW,CAACvH,IAAI,CAAC,EAAE;MAC5DH,IAAI,CAACiF,mBAAmB,CAAC9E,IAAI,EAAEC,KAAK,CAACiD,SAAS,CAAC,CAAC,CAAC;MACjDjD,KAAK,CAACgD,aAAa,GAAG,IAAI;IAC5B;EACF,CAAC;EAEDuE,cAAc,EAAE,SAAAA,CAAUxH,IAAS,EAAEC,KAAU,EAAE;IAC/CA,KAAK,CAAC+C,QAAQ,GAAG,IAAI;EACvB;AACF,CAAC;AAED,MAAMrB,mBAAmB,GAAG;EAC1B8F,YAAYA,CAACzH,IAAS,EAAE;IACtB,MAAM;MAAEE;IAAK,CAAC,GAAGF,IAAI;IAErB,IAAIE,IAAI,CAACwH,IAAI,CAAC7E,IAAI,KAAK,UAAU,IAAI3C,IAAI,CAACyH,QAAQ,CAAC9E,IAAI,KAAK,MAAM,EAAE;MAClE,MAAM/C,CAAC,GAAGD,IAAI,CAAC8F,QAAQ,CAAC,CAAC;MACzB9F,IAAI,CAACiF,mBAAmB,CACtB9E,IAAI,EACJF,CAAC,CAAC8H,gBAAgB,CAChB9H,CAAC,CAACqD,KAAK,CAAE,IAAI,CAASvB,OAAO,CAAC,EAC9B9B,CAAC,CAAC8C,UAAU,CAAC,OAAO,CACtB,CACF,CAAC;IACH;EACF;AACF,CAAC;AAED,MAAMlB,YAAY,GAAG;EACnBX,QAAQ,EAAE,SAAAA,CAAUf,IAAS,EAAE;IAC7BA,IAAI,CAACqH,IAAI,CAAC,CAAC;EACb,CAAC;EAEDQ,eAAe,EAAE,SAAAA,CAAU7H,IAAS,EAAE;IACpC,MAAMF,CAAC,GAAGD,IAAI,CAAC8F,QAAQ,CAAC,CAAC;IAGzB,MAAMmC,QAAQ,GAAG9H,IAAI,CAACE,IAAI,CAAC4H,QAAQ;IAKnCjI,IAAI,CAACiF,mBAAmB,CACtB9E,IAAI,EACJF,CAAC,CAACiI,eAAe,CACfjI,CAAC,CAACe,cAAc,CAAChB,IAAI,CAACyE,eAAe,CAAC,OAAO,CAAC,EAAE,CAACwD,QAAQ,CAAC,CAAC,EAC3D,KACF,CACF,CAAC;EACH;AACF,CAAC", "ignoreList": []}