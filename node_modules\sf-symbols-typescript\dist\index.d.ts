/**
 * Override this interface to limit the SFSymbol types to symbols available in a specific version.
 * 
 * @type {{ SFSymbolsVersion: "1.0" | "1.1" | "2.0" | "2.1" | "2.2" | "3.0" | "3.1" | "3.2" | "3.3" | "4.0" | "4.1" | "4.2" | "5.0" | "5.1" | "5.2" | "5.3" | "6.0"}}
 */
export interface Overrides {}

/**
 * @name SF Symbols 1.0
 * @description These symbols are available on the following platforms:
 * iOS v13.0+,
 * macOS v10.15+,
 * tvOS v13.0+,
 * visionOS v1.0+,
 * watchOS v6.0+
 */
export type SFSymbols1_0 =
  | '0.circle'
  | '0.circle.fill'
  | '0.square'
  | '0.square.fill'
  | '00.circle'
  | '00.circle.fill'
  | '00.square'
  | '00.square.fill'
  | '01.circle'
  | '01.circle.fill'
  | '01.square'
  | '01.square.fill'
  | '02.circle'
  | '02.circle.fill'
  | '02.square'
  | '02.square.fill'
  | '03.circle'
  | '03.circle.fill'
  | '03.square'
  | '03.square.fill'
  | '04.circle'
  | '04.circle.fill'
  | '04.square'
  | '04.square.fill'
  | '05.circle'
  | '05.circle.fill'
  | '05.square'
  | '05.square.fill'
  | '06.circle'
  | '06.circle.fill'
  | '06.square'
  | '06.square.fill'
  | '07.circle'
  | '07.circle.fill'
  | '07.square'
  | '07.square.fill'
  | '08.circle'
  | '08.circle.fill'
  | '08.square'
  | '08.square.fill'
  | '09.circle'
  | '09.circle.fill'
  | '09.square'
  | '09.square.fill'
  | '1.circle'
  | '1.circle.fill'
  | '1.magnifyingglass'
  | '1.square'
  | '1.square.fill'
  | '10.circle'
  | '10.circle.fill'
  | '10.square'
  | '10.square.fill'
  | '11.circle'
  | '11.circle.fill'
  | '11.square'
  | '11.square.fill'
  | '12.circle'
  | '12.circle.fill'
  | '12.square'
  | '12.square.fill'
  | '13.circle'
  | '13.circle.fill'
  | '13.square'
  | '13.square.fill'
  | '14.circle'
  | '14.circle.fill'
  | '14.square'
  | '14.square.fill'
  | '15.circle'
  | '15.circle.fill'
  | '15.square'
  | '15.square.fill'
  | '16.circle'
  | '16.circle.fill'
  | '16.square'
  | '16.square.fill'
  | '17.circle'
  | '17.circle.fill'
  | '17.square'
  | '17.square.fill'
  | '18.circle'
  | '18.circle.fill'
  | '18.square'
  | '18.square.fill'
  | '19.circle'
  | '19.circle.fill'
  | '19.square'
  | '19.square.fill'
  | '2.circle'
  | '2.circle.fill'
  | '2.square'
  | '2.square.fill'
  | '20.circle'
  | '20.circle.fill'
  | '20.square'
  | '20.square.fill'
  | '21.circle'
  | '21.circle.fill'
  | '21.square'
  | '21.square.fill'
  | '22.circle'
  | '22.circle.fill'
  | '22.square'
  | '22.square.fill'
  | '23.circle'
  | '23.circle.fill'
  | '23.square'
  | '23.square.fill'
  | '24.circle'
  | '24.circle.fill'
  | '24.square'
  | '24.square.fill'
  | '25.circle'
  | '25.circle.fill'
  | '25.square'
  | '25.square.fill'
  | '26.circle'
  | '26.circle.fill'
  | '26.square'
  | '26.square.fill'
  | '27.circle'
  | '27.circle.fill'
  | '27.square'
  | '27.square.fill'
  | '28.circle'
  | '28.circle.fill'
  | '28.square'
  | '28.square.fill'
  | '29.circle'
  | '29.circle.fill'
  | '29.square'
  | '29.square.fill'
  | '3.circle'
  | '3.circle.fill'
  | '3.square'
  | '3.square.fill'
  | '30.circle'
  | '30.circle.fill'
  | '30.square'
  | '30.square.fill'
  | '31.circle'
  | '31.circle.fill'
  | '31.square'
  | '31.square.fill'
  | '32.circle'
  | '32.circle.fill'
  | '32.square'
  | '32.square.fill'
  | '33.circle'
  | '33.circle.fill'
  | '33.square'
  | '33.square.fill'
  | '34.circle'
  | '34.circle.fill'
  | '34.square'
  | '34.square.fill'
  | '35.circle'
  | '35.circle.fill'
  | '35.square'
  | '35.square.fill'
  | '36.circle'
  | '36.circle.fill'
  | '36.square'
  | '36.square.fill'
  | '37.circle'
  | '37.circle.fill'
  | '37.square'
  | '37.square.fill'
  | '38.circle'
  | '38.circle.fill'
  | '38.square'
  | '38.square.fill'
  | '39.circle'
  | '39.circle.fill'
  | '39.square'
  | '39.square.fill'
  | '4.alt.circle'
  | '4.alt.circle.fill'
  | '4.alt.square'
  | '4.alt.square.fill'
  | '4.circle'
  | '4.circle.fill'
  | '4.square'
  | '4.square.fill'
  | '40.circle'
  | '40.circle.fill'
  | '40.square'
  | '40.square.fill'
  | '41.circle'
  | '41.circle.fill'
  | '41.square'
  | '41.square.fill'
  | '42.circle'
  | '42.circle.fill'
  | '42.square'
  | '42.square.fill'
  | '43.circle'
  | '43.circle.fill'
  | '43.square'
  | '43.square.fill'
  | '44.circle'
  | '44.circle.fill'
  | '44.square'
  | '44.square.fill'
  | '45.circle'
  | '45.circle.fill'
  | '45.square'
  | '45.square.fill'
  | '46.circle'
  | '46.circle.fill'
  | '46.square'
  | '46.square.fill'
  | '47.circle'
  | '47.circle.fill'
  | '47.square'
  | '47.square.fill'
  | '48.circle'
  | '48.circle.fill'
  | '48.square'
  | '48.square.fill'
  | '49.circle'
  | '49.circle.fill'
  | '49.square'
  | '49.square.fill'
  | '5.circle'
  | '5.circle.fill'
  | '5.square'
  | '5.square.fill'
  | '50.circle'
  | '50.circle.fill'
  | '50.square'
  | '50.square.fill'
  | '6.alt.circle'
  | '6.alt.circle.fill'
  | '6.alt.square'
  | '6.alt.square.fill'
  | '6.circle'
  | '6.circle.fill'
  | '6.square'
  | '6.square.fill'
  | '7.circle'
  | '7.circle.fill'
  | '7.square'
  | '7.square.fill'
  | '8.circle'
  | '8.circle.fill'
  | '8.square'
  | '8.square.fill'
  | '9.alt.circle'
  | '9.alt.circle.fill'
  | '9.alt.square'
  | '9.alt.square.fill'
  | '9.circle'
  | '9.circle.fill'
  | '9.square'
  | '9.square.fill'
  | 'a'
  | 'a.circle'
  | 'a.circle.fill'
  | 'a.square'
  | 'a.square.fill'
  | 'airplane'
  | 'airplayaudio'
  | 'airplayvideo'
  | 'alarm'
  | 'alarm.fill'
  | 'alt'
  | 'ant'
  | 'ant.circle'
  | 'ant.circle.fill'
  | 'ant.fill'
  | 'antenna.radiowaves.left.and.right'
  | 'app'
  | 'app.badge'
  | 'app.badge.fill'
  | 'app.fill'
  | 'app.gift'
  | 'app.gift.fill'
  | 'archivebox'
  | 'archivebox.fill'
  | 'arkit'
  | 'arrow.2.circlepath'
  | 'arrow.2.circlepath.circle'
  | 'arrow.2.circlepath.circle.fill'
  | 'arrow.2.squarepath'
  | 'arrow.3.trianglepath'
  | 'arrow.branch'
  | 'arrow.clockwise'
  | 'arrow.clockwise.circle'
  | 'arrow.clockwise.circle.fill'
  | 'arrow.clockwise.icloud'
  | 'arrow.clockwise.icloud.fill'
  | 'arrow.counterclockwise'
  | 'arrow.counterclockwise.circle'
  | 'arrow.counterclockwise.circle.fill'
  | 'arrow.counterclockwise.icloud'
  | 'arrow.counterclockwise.icloud.fill'
  | 'arrow.down'
  | 'arrow.down.circle'
  | 'arrow.down.circle.fill'
  | 'arrow.down.doc'
  | 'arrow.down.doc.fill'
  | 'arrow.down.left'
  | 'arrow.down.left.circle'
  | 'arrow.down.left.circle.fill'
  | 'arrow.down.left.square'
  | 'arrow.down.left.square.fill'
  | 'arrow.down.left.video'
  | 'arrow.down.left.video.fill'
  | 'arrow.down.right'
  | 'arrow.down.right.and.arrow.up.left'
  | 'arrow.down.right.circle'
  | 'arrow.down.right.circle.fill'
  | 'arrow.down.right.square'
  | 'arrow.down.right.square.fill'
  | 'arrow.down.square'
  | 'arrow.down.square.fill'
  | 'arrow.down.to.line'
  | 'arrow.down.to.line.alt'
  | 'arrow.left'
  | 'arrow.left.and.right'
  | 'arrow.left.and.right.circle'
  | 'arrow.left.and.right.circle.fill'
  | 'arrow.left.and.right.square'
  | 'arrow.left.and.right.square.fill'
  | 'arrow.left.circle'
  | 'arrow.left.circle.fill'
  | 'arrow.left.square'
  | 'arrow.left.square.fill'
  | 'arrow.left.to.line'
  | 'arrow.left.to.line.alt'
  | 'arrow.merge'
  | 'arrow.right'
  | 'arrow.right.arrow.left'
  | 'arrow.right.arrow.left.circle'
  | 'arrow.right.arrow.left.circle.fill'
  | 'arrow.right.arrow.left.square'
  | 'arrow.right.arrow.left.square.fill'
  | 'arrow.right.circle'
  | 'arrow.right.circle.fill'
  | 'arrow.right.square'
  | 'arrow.right.square.fill'
  | 'arrow.right.to.line'
  | 'arrow.right.to.line.alt'
  | 'arrow.swap'
  | 'arrow.turn.down.left'
  | 'arrow.turn.down.right'
  | 'arrow.turn.left.down'
  | 'arrow.turn.left.up'
  | 'arrow.turn.right.down'
  | 'arrow.turn.right.up'
  | 'arrow.turn.up.left'
  | 'arrow.turn.up.right'
  | 'arrow.up'
  | 'arrow.up.and.down'
  | 'arrow.up.and.down.circle'
  | 'arrow.up.and.down.circle.fill'
  | 'arrow.up.and.down.square'
  | 'arrow.up.and.down.square.fill'
  | 'arrow.up.arrow.down'
  | 'arrow.up.arrow.down.circle'
  | 'arrow.up.arrow.down.circle.fill'
  | 'arrow.up.arrow.down.square'
  | 'arrow.up.arrow.down.square.fill'
  | 'arrow.up.bin'
  | 'arrow.up.bin.fill'
  | 'arrow.up.circle'
  | 'arrow.up.circle.fill'
  | 'arrow.up.doc'
  | 'arrow.up.doc.fill'
  | 'arrow.up.left'
  | 'arrow.up.left.and.arrow.down.right'
  | 'arrow.up.left.circle'
  | 'arrow.up.left.circle.fill'
  | 'arrow.up.left.square'
  | 'arrow.up.left.square.fill'
  | 'arrow.up.right'
  | 'arrow.up.right.circle'
  | 'arrow.up.right.circle.fill'
  | 'arrow.up.right.diamond'
  | 'arrow.up.right.diamond.fill'
  | 'arrow.up.right.square'
  | 'arrow.up.right.square.fill'
  | 'arrow.up.right.video'
  | 'arrow.up.right.video.fill'
  | 'arrow.up.square'
  | 'arrow.up.square.fill'
  | 'arrow.up.to.line'
  | 'arrow.up.to.line.alt'
  | 'arrow.uturn.down'
  | 'arrow.uturn.down.circle'
  | 'arrow.uturn.down.circle.fill'
  | 'arrow.uturn.down.square'
  | 'arrow.uturn.down.square.fill'
  | 'arrow.uturn.left'
  | 'arrow.uturn.left.circle'
  | 'arrow.uturn.left.circle.fill'
  | 'arrow.uturn.left.square'
  | 'arrow.uturn.left.square.fill'
  | 'arrow.uturn.right'
  | 'arrow.uturn.right.circle'
  | 'arrow.uturn.right.circle.fill'
  | 'arrow.uturn.right.square'
  | 'arrow.uturn.right.square.fill'
  | 'arrow.uturn.up'
  | 'arrow.uturn.up.circle'
  | 'arrow.uturn.up.circle.fill'
  | 'arrow.uturn.up.square'
  | 'arrow.uturn.up.square.fill'
  | 'arrowshape.turn.up.left'
  | 'arrowshape.turn.up.left.2'
  | 'arrowshape.turn.up.left.2.fill'
  | 'arrowshape.turn.up.left.circle'
  | 'arrowshape.turn.up.left.circle.fill'
  | 'arrowshape.turn.up.left.fill'
  | 'arrowshape.turn.up.right'
  | 'arrowshape.turn.up.right.circle'
  | 'arrowshape.turn.up.right.circle.fill'
  | 'arrowshape.turn.up.right.fill'
  | 'arrowtriangle.down'
  | 'arrowtriangle.down.circle'
  | 'arrowtriangle.down.circle.fill'
  | 'arrowtriangle.down.fill'
  | 'arrowtriangle.down.square'
  | 'arrowtriangle.down.square.fill'
  | 'arrowtriangle.left'
  | 'arrowtriangle.left.circle'
  | 'arrowtriangle.left.circle.fill'
  | 'arrowtriangle.left.fill'
  | 'arrowtriangle.left.square'
  | 'arrowtriangle.left.square.fill'
  | 'arrowtriangle.right'
  | 'arrowtriangle.right.circle'
  | 'arrowtriangle.right.circle.fill'
  | 'arrowtriangle.right.fill'
  | 'arrowtriangle.right.square'
  | 'arrowtriangle.right.square.fill'
  | 'arrowtriangle.up'
  | 'arrowtriangle.up.circle'
  | 'arrowtriangle.up.circle.fill'
  | 'arrowtriangle.up.fill'
  | 'arrowtriangle.up.square'
  | 'arrowtriangle.up.square.fill'
  | 'asterisk.circle'
  | 'asterisk.circle.fill'
  | 'at'
  | 'at.badge.minus'
  | 'at.badge.plus'
  | 'australsign.circle'
  | 'australsign.circle.fill'
  | 'australsign.square'
  | 'australsign.square.fill'
  | 'b.circle'
  | 'b.circle.fill'
  | 'b.square'
  | 'b.square.fill'
  | 'backward'
  | 'backward.end'
  | 'backward.end.alt'
  | 'backward.end.alt.fill'
  | 'backward.end.fill'
  | 'backward.fill'
  | 'badge.plus.radiowaves.right'
  | 'bag'
  | 'bag.badge.minus'
  | 'bag.badge.plus'
  | 'bag.fill'
  | 'bag.fill.badge.minus'
  | 'bag.fill.badge.plus'
  | 'bahtsign.circle'
  | 'bahtsign.circle.fill'
  | 'bahtsign.square'
  | 'bahtsign.square.fill'
  | 'bandage'
  | 'bandage.fill'
  | 'barcode'
  | 'barcode.viewfinder'
  | 'battery.0'
  | 'battery.100'
  | 'battery.25'
  | 'bed.double'
  | 'bed.double.fill'
  | 'bell'
  | 'bell.circle'
  | 'bell.circle.fill'
  | 'bell.fill'
  | 'bell.slash'
  | 'bell.slash.fill'
  | 'bin.xmark'
  | 'bin.xmark.fill'
  | 'bitcoinsign.circle'
  | 'bitcoinsign.circle.fill'
  | 'bitcoinsign.square'
  | 'bitcoinsign.square.fill'
  | 'bold'
  | 'bold.italic.underline'
  | 'bold.underline'
  | 'bolt'
  | 'bolt.badge.a'
  | 'bolt.badge.a.fill'
  | 'bolt.circle'
  | 'bolt.circle.fill'
  | 'bolt.fill'
  | 'bolt.horizontal'
  | 'bolt.horizontal.circle'
  | 'bolt.horizontal.circle.fill'
  | 'bolt.horizontal.fill'
  | 'bolt.horizontal.icloud'
  | 'bolt.horizontal.icloud.fill'
  | 'bolt.slash'
  | 'bolt.slash.fill'
  | 'book'
  | 'book.circle'
  | 'book.circle.fill'
  | 'book.fill'
  | 'bookmark'
  | 'bookmark.fill'
  | 'briefcase'
  | 'briefcase.fill'
  | 'bubble.left'
  | 'bubble.left.and.bubble.right'
  | 'bubble.left.and.bubble.right.fill'
  | 'bubble.left.fill'
  | 'bubble.middle.bottom'
  | 'bubble.middle.bottom.fill'
  | 'bubble.middle.top'
  | 'bubble.middle.top.fill'
  | 'bubble.right'
  | 'bubble.right.fill'
  | 'burn'
  | 'burst'
  | 'burst.fill'
  | 'c.circle'
  | 'c.circle.fill'
  | 'c.square'
  | 'c.square.fill'
  | 'calendar'
  | 'calendar.badge.minus'
  | 'calendar.badge.plus'
  | 'calendar.circle'
  | 'calendar.circle.fill'
  | 'camera'
  | 'camera.circle'
  | 'camera.circle.fill'
  | 'camera.fill'
  | 'camera.on.rectangle'
  | 'camera.on.rectangle.fill'
  | 'camera.rotate'
  | 'camera.rotate.fill'
  | 'camera.viewfinder'
  | 'capslock'
  | 'capslock.fill'
  | 'capsule'
  | 'capsule.fill'
  | 'captions.bubble'
  | 'captions.bubble.fill'
  | 'car.fill'
  | 'cart'
  | 'cart.badge.minus'
  | 'cart.badge.plus'
  | 'cart.fill'
  | 'cart.fill.badge.minus'
  | 'cart.fill.badge.plus'
  | 'cedisign.circle'
  | 'cedisign.circle.fill'
  | 'cedisign.square'
  | 'cedisign.square.fill'
  | 'centsign.circle'
  | 'centsign.circle.fill'
  | 'centsign.square'
  | 'centsign.square.fill'
  | 'chart.bar'
  | 'chart.bar.fill'
  | 'chart.pie'
  | 'chart.pie.fill'
  | 'checkmark'
  | 'checkmark.circle'
  | 'checkmark.circle.fill'
  | 'checkmark.rectangle'
  | 'checkmark.rectangle.fill'
  | 'checkmark.seal'
  | 'checkmark.seal.fill'
  | 'checkmark.shield'
  | 'checkmark.shield.fill'
  | 'checkmark.square'
  | 'checkmark.square.fill'
  | 'chevron.compact.down'
  | 'chevron.compact.left'
  | 'chevron.compact.right'
  | 'chevron.compact.up'
  | 'chevron.down'
  | 'chevron.down.circle'
  | 'chevron.down.circle.fill'
  | 'chevron.down.square'
  | 'chevron.down.square.fill'
  | 'chevron.left'
  | 'chevron.left.2'
  | 'chevron.left.circle'
  | 'chevron.left.circle.fill'
  | 'chevron.left.slash.chevron.right'
  | 'chevron.left.square'
  | 'chevron.left.square.fill'
  | 'chevron.right'
  | 'chevron.right.2'
  | 'chevron.right.circle'
  | 'chevron.right.circle.fill'
  | 'chevron.right.square'
  | 'chevron.right.square.fill'
  | 'chevron.up'
  | 'chevron.up.chevron.down'
  | 'chevron.up.circle'
  | 'chevron.up.circle.fill'
  | 'chevron.up.square'
  | 'chevron.up.square.fill'
  | 'circle'
  | 'circle.bottomthird.split'
  | 'circle.fill'
  | 'circle.grid.3x3'
  | 'circle.grid.3x3.fill'
  | 'circle.grid.hex'
  | 'circle.grid.hex.fill'
  | 'circle.lefthalf.fill'
  | 'circle.righthalf.fill'
  | 'clear'
  | 'clear.fill'
  | 'clock'
  | 'clock.fill'
  | 'cloud'
  | 'cloud.bolt'
  | 'cloud.bolt.fill'
  | 'cloud.bolt.rain'
  | 'cloud.bolt.rain.fill'
  | 'cloud.drizzle'
  | 'cloud.drizzle.fill'
  | 'cloud.fill'
  | 'cloud.fog'
  | 'cloud.fog.fill'
  | 'cloud.hail'
  | 'cloud.hail.fill'
  | 'cloud.heavyrain'
  | 'cloud.heavyrain.fill'
  | 'cloud.moon'
  | 'cloud.moon.bolt'
  | 'cloud.moon.bolt.fill'
  | 'cloud.moon.fill'
  | 'cloud.moon.rain'
  | 'cloud.moon.rain.fill'
  | 'cloud.rain'
  | 'cloud.rain.fill'
  | 'cloud.sleet'
  | 'cloud.sleet.fill'
  | 'cloud.snow'
  | 'cloud.snow.fill'
  | 'cloud.sun'
  | 'cloud.sun.bolt'
  | 'cloud.sun.bolt.fill'
  | 'cloud.sun.fill'
  | 'cloud.sun.rain'
  | 'cloud.sun.rain.fill'
  | 'coloncurrencysign.circle'
  | 'coloncurrencysign.circle.fill'
  | 'coloncurrencysign.square'
  | 'coloncurrencysign.square.fill'
  | 'command'
  | 'control'
  | 'creditcard'
  | 'creditcard.fill'
  | 'crop'
  | 'crop.rotate'
  | 'cruzeirosign.circle'
  | 'cruzeirosign.circle.fill'
  | 'cruzeirosign.square'
  | 'cruzeirosign.square.fill'
  | 'cube'
  | 'cube.box'
  | 'cube.box.fill'
  | 'cube.fill'
  | 'cursor.rays'
  | 'd.circle'
  | 'd.circle.fill'
  | 'd.square'
  | 'd.square.fill'
  | 'decrease.indent'
  | 'decrease.quotelevel'
  | 'delete.left'
  | 'delete.left.fill'
  | 'delete.right'
  | 'delete.right.fill'
  | 'desktopcomputer'
  | 'dial'
  | 'dial.fill'
  | 'divide'
  | 'divide.circle'
  | 'divide.circle.fill'
  | 'divide.square'
  | 'divide.square.fill'
  | 'doc'
  | 'doc.append'
  | 'doc.circle'
  | 'doc.circle.fill'
  | 'doc.fill'
  | 'doc.on.clipboard'
  | 'doc.on.clipboard.fill'
  | 'doc.on.doc'
  | 'doc.on.doc.fill'
  | 'doc.plaintext'
  | 'doc.richtext'
  | 'doc.text'
  | 'doc.text.fill'
  | 'doc.text.magnifyingglass'
  | 'doc.text.viewfinder'
  | 'dollarsign.circle'
  | 'dollarsign.circle.fill'
  | 'dollarsign.square'
  | 'dollarsign.square.fill'
  | 'dongsign.circle'
  | 'dongsign.circle.fill'
  | 'dongsign.square'
  | 'dongsign.square.fill'
  | 'dot.radiowaves.left.and.right'
  | 'dot.radiowaves.right'
  | 'dot.square'
  | 'dot.square.fill'
  | 'drop.triangle'
  | 'drop.triangle.fill'
  | 'e.circle'
  | 'e.circle.fill'
  | 'e.square'
  | 'e.square.fill'
  | 'ear'
  | 'eject'
  | 'eject.fill'
  | 'ellipses.bubble'
  | 'ellipses.bubble.fill'
  | 'ellipsis'
  | 'ellipsis.circle'
  | 'ellipsis.circle.fill'
  | 'envelope'
  | 'envelope.badge'
  | 'envelope.badge.fill'
  | 'envelope.circle'
  | 'envelope.circle.fill'
  | 'envelope.fill'
  | 'envelope.open'
  | 'envelope.open.fill'
  | 'equal'
  | 'equal.circle'
  | 'equal.circle.fill'
  | 'equal.square'
  | 'equal.square.fill'
  | 'escape'
  | 'eurosign.circle'
  | 'eurosign.circle.fill'
  | 'eurosign.square'
  | 'eurosign.square.fill'
  | 'exclamationmark'
  | 'exclamationmark.bubble'
  | 'exclamationmark.bubble.fill'
  | 'exclamationmark.circle'
  | 'exclamationmark.circle.fill'
  | 'exclamationmark.icloud'
  | 'exclamationmark.icloud.fill'
  | 'exclamationmark.octagon'
  | 'exclamationmark.octagon.fill'
  | 'exclamationmark.shield'
  | 'exclamationmark.shield.fill'
  | 'exclamationmark.square'
  | 'exclamationmark.square.fill'
  | 'exclamationmark.triangle'
  | 'exclamationmark.triangle.fill'
  | 'eye'
  | 'eye.fill'
  | 'eye.slash'
  | 'eye.slash.fill'
  | 'eyedropper'
  | 'eyedropper.full'
  | 'eyedropper.halffull'
  | 'eyeglasses'
  | 'f.circle'
  | 'f.circle.fill'
  | 'f.cursive'
  | 'f.cursive.circle'
  | 'f.cursive.circle.fill'
  | 'f.square'
  | 'f.square.fill'
  | 'faceid'
  | 'film'
  | 'film.fill'
  | 'flag'
  | 'flag.circle'
  | 'flag.circle.fill'
  | 'flag.fill'
  | 'flag.slash'
  | 'flag.slash.fill'
  | 'flame'
  | 'flame.fill'
  | 'florinsign.circle'
  | 'florinsign.circle.fill'
  | 'florinsign.square'
  | 'florinsign.square.fill'
  | 'flowchart'
  | 'flowchart.fill'
  | 'folder'
  | 'folder.badge.minus'
  | 'folder.badge.person.crop'
  | 'folder.badge.plus'
  | 'folder.circle'
  | 'folder.circle.fill'
  | 'folder.fill'
  | 'folder.fill.badge.minus'
  | 'folder.fill.badge.person.crop'
  | 'folder.fill.badge.plus'
  | 'forward'
  | 'forward.end'
  | 'forward.end.alt'
  | 'forward.end.alt.fill'
  | 'forward.end.fill'
  | 'forward.fill'
  | 'francsign.circle'
  | 'francsign.circle.fill'
  | 'francsign.square'
  | 'francsign.square.fill'
  | 'function'
  | 'fx'
  | 'g.circle'
  | 'g.circle.fill'
  | 'g.square'
  | 'g.square.fill'
  | 'gamecontroller'
  | 'gamecontroller.fill'
  | 'gauge'
  | 'gauge.badge.minus'
  | 'gauge.badge.plus'
  | 'gear'
  | 'gift'
  | 'gift.fill'
  | 'globe'
  | 'gobackward'
  | 'gobackward.10'
  | 'gobackward.10.ar'
  | 'gobackward.10.hi'
  | 'gobackward.15'
  | 'gobackward.15.ar'
  | 'gobackward.15.hi'
  | 'gobackward.30'
  | 'gobackward.30.ar'
  | 'gobackward.30.hi'
  | 'gobackward.45'
  | 'gobackward.45.ar'
  | 'gobackward.45.hi'
  | 'gobackward.60'
  | 'gobackward.60.ar'
  | 'gobackward.60.hi'
  | 'gobackward.75'
  | 'gobackward.75.ar'
  | 'gobackward.75.hi'
  | 'gobackward.90'
  | 'gobackward.90.ar'
  | 'gobackward.90.hi'
  | 'gobackward.minus'
  | 'goforward'
  | 'goforward.10'
  | 'goforward.10.ar'
  | 'goforward.10.hi'
  | 'goforward.15'
  | 'goforward.15.ar'
  | 'goforward.15.hi'
  | 'goforward.30'
  | 'goforward.30.ar'
  | 'goforward.30.hi'
  | 'goforward.45'
  | 'goforward.45.ar'
  | 'goforward.45.hi'
  | 'goforward.60'
  | 'goforward.60.ar'
  | 'goforward.60.hi'
  | 'goforward.75'
  | 'goforward.75.ar'
  | 'goforward.75.hi'
  | 'goforward.90'
  | 'goforward.90.ar'
  | 'goforward.90.hi'
  | 'goforward.plus'
  | 'greaterthan'
  | 'greaterthan.circle'
  | 'greaterthan.circle.fill'
  | 'greaterthan.square'
  | 'greaterthan.square.fill'
  | 'grid'
  | 'grid.circle'
  | 'grid.circle.fill'
  | 'guaranisign.circle'
  | 'guaranisign.circle.fill'
  | 'guaranisign.square'
  | 'guaranisign.square.fill'
  | 'guitars'
  | 'h.circle'
  | 'h.circle.fill'
  | 'h.square'
  | 'h.square.fill'
  | 'hammer'
  | 'hammer.fill'
  | 'hand.draw'
  | 'hand.draw.fill'
  | 'hand.point.left'
  | 'hand.point.left.fill'
  | 'hand.point.right'
  | 'hand.point.right.fill'
  | 'hand.raised'
  | 'hand.raised.fill'
  | 'hand.raised.slash'
  | 'hand.raised.slash.fill'
  | 'hand.thumbsdown'
  | 'hand.thumbsdown.fill'
  | 'hand.thumbsup'
  | 'hand.thumbsup.fill'
  | 'hare'
  | 'hare.fill'
  | 'headphones'
  | 'heart'
  | 'heart.circle'
  | 'heart.circle.fill'
  | 'heart.fill'
  | 'heart.slash'
  | 'heart.slash.circle'
  | 'heart.slash.circle.fill'
  | 'heart.slash.fill'
  | 'helm'
  | 'hexagon'
  | 'hexagon.fill'
  | 'hifispeaker'
  | 'hifispeaker.fill'
  | 'hourglass'
  | 'hourglass.bottomhalf.fill'
  | 'hourglass.tophalf.fill'
  | 'house'
  | 'house.fill'
  | 'hryvniasign.circle'
  | 'hryvniasign.circle.fill'
  | 'hryvniasign.square'
  | 'hryvniasign.square.fill'
  | 'hurricane'
  | 'i.circle'
  | 'i.circle.fill'
  | 'i.square'
  | 'i.square.fill'
  | 'icloud'
  | 'icloud.and.arrow.down'
  | 'icloud.and.arrow.down.fill'
  | 'icloud.and.arrow.up'
  | 'icloud.and.arrow.up.fill'
  | 'icloud.circle'
  | 'icloud.circle.fill'
  | 'icloud.fill'
  | 'icloud.slash'
  | 'icloud.slash.fill'
  | 'increase.indent'
  | 'increase.quotelevel'
  | 'indianrupeesign.circle'
  | 'indianrupeesign.circle.fill'
  | 'indianrupeesign.square'
  | 'indianrupeesign.square.fill'
  | 'info'
  | 'info.circle'
  | 'info.circle.fill'
  | 'italic'
  | 'j.circle'
  | 'j.circle.fill'
  | 'j.square'
  | 'j.square.fill'
  | 'k.circle'
  | 'k.circle.fill'
  | 'k.square'
  | 'k.square.fill'
  | 'keyboard'
  | 'keyboard.chevron.compact.down'
  | 'kipsign.circle'
  | 'kipsign.circle.fill'
  | 'kipsign.square'
  | 'kipsign.square.fill'
  | 'l.circle'
  | 'l.circle.fill'
  | 'l.square'
  | 'l.square.fill'
  | 'largecircle.fill.circle'
  | 'larisign.circle'
  | 'larisign.circle.fill'
  | 'larisign.square'
  | 'larisign.square.fill'
  | 'lasso'
  | 'leaf.arrow.circlepath'
  | 'lessthan'
  | 'lessthan.circle'
  | 'lessthan.circle.fill'
  | 'lessthan.square'
  | 'lessthan.square.fill'
  | 'light.max'
  | 'light.min'
  | 'lightbulb'
  | 'lightbulb.fill'
  | 'lightbulb.slash'
  | 'lightbulb.slash.fill'
  | 'line.horizontal.3'
  | 'line.horizontal.3.decrease'
  | 'line.horizontal.3.decrease.circle'
  | 'line.horizontal.3.decrease.circle.fill'
  | 'link'
  | 'link.circle'
  | 'link.circle.fill'
  | 'link.icloud'
  | 'link.icloud.fill'
  | 'lirasign.circle'
  | 'lirasign.circle.fill'
  | 'lirasign.square'
  | 'lirasign.square.fill'
  | 'list.bullet'
  | 'list.bullet.below.rectangle'
  | 'list.bullet.indent'
  | 'list.dash'
  | 'list.number'
  | 'list.number.rtl'
  | 'livephoto'
  | 'livephoto.play'
  | 'livephoto.slash'
  | 'location'
  | 'location.circle'
  | 'location.circle.fill'
  | 'location.fill'
  | 'location.north'
  | 'location.north.fill'
  | 'location.north.line'
  | 'location.north.line.fill'
  | 'location.slash'
  | 'location.slash.fill'
  | 'lock'
  | 'lock.circle'
  | 'lock.circle.fill'
  | 'lock.fill'
  | 'lock.icloud'
  | 'lock.icloud.fill'
  | 'lock.open'
  | 'lock.open.fill'
  | 'lock.rotation'
  | 'lock.rotation.open'
  | 'lock.shield'
  | 'lock.shield.fill'
  | 'lock.slash'
  | 'lock.slash.fill'
  | 'm.circle'
  | 'm.circle.fill'
  | 'm.square'
  | 'm.square.fill'
  | 'macwindow'
  | 'magnifyingglass'
  | 'magnifyingglass.circle'
  | 'magnifyingglass.circle.fill'
  | 'manatsign.circle'
  | 'manatsign.circle.fill'
  | 'manatsign.square'
  | 'manatsign.square.fill'
  | 'map'
  | 'map.fill'
  | 'mappin'
  | 'mappin.and.ellipse'
  | 'mappin.slash'
  | 'memories'
  | 'memories.badge.minus'
  | 'memories.badge.plus'
  | 'message'
  | 'message.circle'
  | 'message.circle.fill'
  | 'message.fill'
  | 'metronome'
  | 'mic'
  | 'mic.circle'
  | 'mic.circle.fill'
  | 'mic.fill'
  | 'mic.slash'
  | 'mic.slash.fill'
  | 'millsign.circle'
  | 'millsign.circle.fill'
  | 'millsign.square'
  | 'millsign.square.fill'
  | 'minus'
  | 'minus.circle'
  | 'minus.circle.fill'
  | 'minus.magnifyingglass'
  | 'minus.rectangle'
  | 'minus.rectangle.fill'
  | 'minus.slash.plus'
  | 'minus.square'
  | 'minus.square.fill'
  | 'moon'
  | 'moon.circle'
  | 'moon.circle.fill'
  | 'moon.fill'
  | 'moon.stars'
  | 'moon.stars.fill'
  | 'moon.zzz'
  | 'moon.zzz.fill'
  | 'multiply'
  | 'multiply.circle'
  | 'multiply.circle.fill'
  | 'multiply.square'
  | 'multiply.square.fill'
  | 'music.house'
  | 'music.house.fill'
  | 'music.mic'
  | 'music.note'
  | 'music.note.list'
  | 'n.circle'
  | 'n.circle.fill'
  | 'n.square'
  | 'n.square.fill'
  | 'nairasign.circle'
  | 'nairasign.circle.fill'
  | 'nairasign.square'
  | 'nairasign.square.fill'
  | 'nosign'
  | 'number'
  | 'number.circle'
  | 'number.circle.fill'
  | 'number.square'
  | 'number.square.fill'
  | 'o.circle'
  | 'o.circle.fill'
  | 'o.square'
  | 'o.square.fill'
  | 'option'
  | 'p.circle'
  | 'p.circle.fill'
  | 'p.square'
  | 'p.square.fill'
  | 'paintbrush'
  | 'paintbrush.fill'
  | 'pano'
  | 'pano.fill'
  | 'paperclip'
  | 'paperplane'
  | 'paperplane.fill'
  | 'paragraph'
  | 'pause'
  | 'pause.circle'
  | 'pause.circle.fill'
  | 'pause.fill'
  | 'pause.rectangle'
  | 'pause.rectangle.fill'
  | 'pencil'
  | 'pencil.and.ellipsis.rectangle'
  | 'pencil.and.outline'
  | 'pencil.circle'
  | 'pencil.circle.fill'
  | 'pencil.slash'
  | 'pencil.tip'
  | 'pencil.tip.crop.circle'
  | 'pencil.tip.crop.circle.badge.minus'
  | 'pencil.tip.crop.circle.badge.plus'
  | 'percent'
  | 'person'
  | 'person.2'
  | 'person.2.fill'
  | 'person.2.square.stack'
  | 'person.2.square.stack.fill'
  | 'person.3'
  | 'person.3.fill'
  | 'person.badge.minus'
  | 'person.badge.minus.fill'
  | 'person.badge.plus'
  | 'person.badge.plus.fill'
  | 'person.circle'
  | 'person.circle.fill'
  | 'person.crop.circle'
  | 'person.crop.circle.badge.checkmark'
  | 'person.crop.circle.badge.exclam'
  | 'person.crop.circle.badge.minus'
  | 'person.crop.circle.badge.plus'
  | 'person.crop.circle.badge.xmark'
  | 'person.crop.circle.fill'
  | 'person.crop.circle.fill.badge.checkmark'
  | 'person.crop.circle.fill.badge.exclam'
  | 'person.crop.circle.fill.badge.minus'
  | 'person.crop.circle.fill.badge.plus'
  | 'person.crop.circle.fill.badge.xmark'
  | 'person.crop.rectangle'
  | 'person.crop.rectangle.fill'
  | 'person.crop.square'
  | 'person.crop.square.fill'
  | 'person.fill'
  | 'person.icloud'
  | 'person.icloud.fill'
  | 'personalhotspot'
  | 'perspective'
  | 'pesetasign.circle'
  | 'pesetasign.circle.fill'
  | 'pesetasign.square'
  | 'pesetasign.square.fill'
  | 'pesosign.circle'
  | 'pesosign.circle.fill'
  | 'pesosign.square'
  | 'pesosign.square.fill'
  | 'phone'
  | 'phone.arrow.down.left'
  | 'phone.arrow.right'
  | 'phone.arrow.up.right'
  | 'phone.badge.plus'
  | 'phone.circle'
  | 'phone.circle.fill'
  | 'phone.down'
  | 'phone.down.circle'
  | 'phone.down.circle.fill'
  | 'phone.down.fill'
  | 'phone.fill'
  | 'phone.fill.arrow.down.left'
  | 'phone.fill.arrow.right'
  | 'phone.fill.arrow.up.right'
  | 'phone.fill.badge.plus'
  | 'photo'
  | 'photo.fill'
  | 'photo.fill.on.rectangle.fill'
  | 'photo.on.rectangle'
  | 'pin'
  | 'pin.fill'
  | 'pin.slash'
  | 'pin.slash.fill'
  | 'play'
  | 'play.circle'
  | 'play.circle.fill'
  | 'play.fill'
  | 'play.rectangle'
  | 'play.rectangle.fill'
  | 'playpause'
  | 'playpause.fill'
  | 'plus'
  | 'plus.app'
  | 'plus.app.fill'
  | 'plus.bubble'
  | 'plus.bubble.fill'
  | 'plus.circle'
  | 'plus.circle.fill'
  | 'plus.magnifyingglass'
  | 'plus.rectangle'
  | 'plus.rectangle.fill'
  | 'plus.rectangle.fill.on.rectangle.fill'
  | 'plus.rectangle.on.rectangle'
  | 'plus.slash.minus'
  | 'plus.square'
  | 'plus.square.fill'
  | 'plus.square.fill.on.square.fill'
  | 'plus.square.on.square'
  | 'plusminus'
  | 'plusminus.circle'
  | 'plusminus.circle.fill'
  | 'power'
  | 'printer'
  | 'printer.fill'
  | 'projective'
  | 'purchased'
  | 'purchased.circle'
  | 'purchased.circle.fill'
  | 'q.circle'
  | 'q.circle.fill'
  | 'q.square'
  | 'q.square.fill'
  | 'qrcode'
  | 'qrcode.viewfinder'
  | 'questionmark'
  | 'questionmark.circle'
  | 'questionmark.circle.fill'
  | 'questionmark.diamond'
  | 'questionmark.diamond.fill'
  | 'questionmark.square'
  | 'questionmark.square.fill'
  | 'questionmark.video'
  | 'questionmark.video.fill'
  | 'questionmark.video.fill.rtl'
  | 'questionmark.video.rtl'
  | 'quote.bubble'
  | 'quote.bubble.fill'
  | 'r.circle'
  | 'r.circle.fill'
  | 'r.square'
  | 'r.square.fill'
  | 'radiowaves.left'
  | 'radiowaves.right'
  | 'rays'
  | 'recordingtape'
  | 'rectangle'
  | 'rectangle.3.offgrid'
  | 'rectangle.3.offgrid.fill'
  | 'rectangle.and.arrow.up.right.and.arrow.down.left'
  | 'rectangle.and.arrow.up.right.and.arrow.down.left.slash'
  | 'rectangle.and.paperclip'
  | 'rectangle.badge.checkmark'
  | 'rectangle.badge.xmark'
  | 'rectangle.compress.vertical'
  | 'rectangle.dock'
  | 'rectangle.expand.vertical'
  | 'rectangle.fill'
  | 'rectangle.fill.badge.checkmark'
  | 'rectangle.fill.badge.xmark'
  | 'rectangle.fill.on.rectangle.angled.fill'
  | 'rectangle.fill.on.rectangle.fill'
  | 'rectangle.grid.1x2'
  | 'rectangle.grid.1x2.fill'
  | 'rectangle.grid.2x2'
  | 'rectangle.grid.2x2.fill'
  | 'rectangle.grid.3x2'
  | 'rectangle.grid.3x2.fill'
  | 'rectangle.on.rectangle'
  | 'rectangle.on.rectangle.angled'
  | 'rectangle.split.3x1'
  | 'rectangle.split.3x1.fill'
  | 'rectangle.split.3x3'
  | 'rectangle.split.3x3.fill'
  | 'rectangle.stack'
  | 'rectangle.stack.badge.minus'
  | 'rectangle.stack.badge.person.crop'
  | 'rectangle.stack.badge.plus'
  | 'rectangle.stack.fill'
  | 'rectangle.stack.fill.badge.minus'
  | 'rectangle.stack.fill.badge.person.crop'
  | 'rectangle.stack.fill.badge.plus'
  | 'rectangle.stack.person.crop'
  | 'rectangle.stack.person.crop.fill'
  | 'repeat'
  | 'repeat.1'
  | 'return'
  | 'rhombus'
  | 'rhombus.fill'
  | 'rosette'
  | 'rotate.left'
  | 'rotate.left.fill'
  | 'rotate.right'
  | 'rotate.right.fill'
  | 'rublesign.circle'
  | 'rublesign.circle.fill'
  | 'rublesign.square'
  | 'rublesign.square.fill'
  | 'rupeesign.circle'
  | 'rupeesign.circle.fill'
  | 'rupeesign.square'
  | 'rupeesign.square.fill'
  | 's.circle'
  | 's.circle.fill'
  | 's.square'
  | 's.square.fill'
  | 'safari'
  | 'safari.fill'
  | 'scissors'
  | 'scope'
  | 'scribble'
  | 'selection.pin.in.out'
  | 'sheqelsign.circle'
  | 'sheqelsign.circle.fill'
  | 'sheqelsign.square'
  | 'sheqelsign.square.fill'
  | 'shield'
  | 'shield.fill'
  | 'shield.lefthalf.fill'
  | 'shield.slash'
  | 'shield.slash.fill'
  | 'shift'
  | 'shift.fill'
  | 'shuffle'
  | 'sidebar.left'
  | 'sidebar.right'
  | 'signature'
  | 'skew'
  | 'slash.circle'
  | 'slash.circle.fill'
  | 'slider.horizontal.3'
  | 'slider.horizontal.below.rectangle'
  | 'slowmo'
  | 'smallcircle.circle'
  | 'smallcircle.circle.fill'
  | 'smallcircle.fill.circle'
  | 'smallcircle.fill.circle.fill'
  | 'smiley'
  | 'smiley.fill'
  | 'smoke'
  | 'smoke.fill'
  | 'snow'
  | 'sparkles'
  | 'speaker'
  | 'speaker.1'
  | 'speaker.1.fill'
  | 'speaker.2'
  | 'speaker.2.fill'
  | 'speaker.3'
  | 'speaker.3.fill'
  | 'speaker.fill'
  | 'speaker.slash'
  | 'speaker.slash.fill'
  | 'speaker.slash.fill.rtl'
  | 'speaker.slash.rtl'
  | 'speaker.zzz'
  | 'speaker.zzz.fill'
  | 'speaker.zzz.fill.rtl'
  | 'speaker.zzz.rtl'
  | 'speedometer'
  | 'sportscourt'
  | 'sportscourt.fill'
  | 'square'
  | 'square.and.arrow.down'
  | 'square.and.arrow.down.fill'
  | 'square.and.arrow.down.on.square'
  | 'square.and.arrow.down.on.square.fill'
  | 'square.and.arrow.up'
  | 'square.and.arrow.up.fill'
  | 'square.and.arrow.up.on.square'
  | 'square.and.arrow.up.on.square.fill'
  | 'square.and.line.vertical.and.square'
  | 'square.and.line.vertical.and.square.fill'
  | 'square.and.pencil'
  | 'square.fill'
  | 'square.fill.and.line.vertical.and.square'
  | 'square.fill.and.line.vertical.square.fill'
  | 'square.fill.on.circle.fill'
  | 'square.fill.on.square.fill'
  | 'square.grid.2x2'
  | 'square.grid.2x2.fill'
  | 'square.grid.3x2'
  | 'square.grid.3x2.fill'
  | 'square.grid.4x3.fill'
  | 'square.lefthalf.fill'
  | 'square.on.circle'
  | 'square.on.square'
  | 'square.righthalf.fill'
  | 'square.split.1x2'
  | 'square.split.1x2.fill'
  | 'square.split.2x1'
  | 'square.split.2x1.fill'
  | 'square.split.2x2'
  | 'square.split.2x2.fill'
  | 'square.stack'
  | 'square.stack.3d.down.dottedline'
  | 'square.stack.3d.down.right'
  | 'square.stack.3d.down.right.fill'
  | 'square.stack.3d.up'
  | 'square.stack.3d.up.fill'
  | 'square.stack.3d.up.slash'
  | 'square.stack.3d.up.slash.fill'
  | 'square.stack.fill'
  | 'squares.below.rectangle'
  | 'star'
  | 'star.circle'
  | 'star.circle.fill'
  | 'star.fill'
  | 'star.lefthalf.fill'
  | 'star.slash'
  | 'star.slash.fill'
  | 'staroflife'
  | 'staroflife.fill'
  | 'sterlingsign.circle'
  | 'sterlingsign.circle.fill'
  | 'sterlingsign.square'
  | 'sterlingsign.square.fill'
  | 'stop'
  | 'stop.circle'
  | 'stop.circle.fill'
  | 'stop.fill'
  | 'stopwatch'
  | 'stopwatch.fill'
  | 'strikethrough'
  | 'suit.club'
  | 'suit.club.fill'
  | 'suit.diamond'
  | 'suit.diamond.fill'
  | 'suit.heart'
  | 'suit.heart.fill'
  | 'suit.spade'
  | 'suit.spade.fill'
  | 'sum'
  | 'sun.dust'
  | 'sun.dust.fill'
  | 'sun.haze'
  | 'sun.haze.fill'
  | 'sun.max'
  | 'sun.max.fill'
  | 'sun.min'
  | 'sun.min.fill'
  | 'sunrise'
  | 'sunrise.fill'
  | 'sunset'
  | 'sunset.fill'
  | 't.bubble'
  | 't.bubble.fill'
  | 't.circle'
  | 't.circle.fill'
  | 't.square'
  | 't.square.fill'
  | 'table'
  | 'table.badge.more'
  | 'table.badge.more.fill'
  | 'table.fill'
  | 'tag'
  | 'tag.circle'
  | 'tag.circle.fill'
  | 'tag.fill'
  | 'teletype'
  | 'teletype.answer'
  | 'tengesign.circle'
  | 'tengesign.circle.fill'
  | 'tengesign.square'
  | 'tengesign.square.fill'
  | 'text.aligncenter'
  | 'text.alignleft'
  | 'text.alignright'
  | 'text.append'
  | 'text.badge.checkmark'
  | 'text.badge.minus'
  | 'text.badge.plus'
  | 'text.badge.star'
  | 'text.badge.xmark'
  | 'text.bubble'
  | 'text.bubble.fill'
  | 'text.cursor'
  | 'text.insert'
  | 'text.justify'
  | 'text.justifyleft'
  | 'text.justifyright'
  | 'text.quote'
  | 'textbox'
  | 'textformat'
  | 'textformat.123'
  | 'textformat.abc'
  | 'textformat.abc.dottedunderline'
  | 'textformat.alt'
  | 'textformat.size'
  | 'textformat.subscript'
  | 'textformat.superscript'
  | 'thermometer'
  | 'thermometer.snowflake'
  | 'thermometer.sun'
  | 'timelapse'
  | 'timer'
  | 'tornado'
  | 'tortoise'
  | 'tortoise.fill'
  | 'tram.fill'
  | 'trash'
  | 'trash.circle'
  | 'trash.circle.fill'
  | 'trash.fill'
  | 'trash.slash'
  | 'trash.slash.fill'
  | 'tray'
  | 'tray.2'
  | 'tray.2.fill'
  | 'tray.and.arrow.down'
  | 'tray.and.arrow.down.fill'
  | 'tray.and.arrow.up'
  | 'tray.and.arrow.up.fill'
  | 'tray.fill'
  | 'tray.full'
  | 'tray.full.fill'
  | 'triangle'
  | 'triangle.fill'
  | 'triangle.lefthalf.fill'
  | 'triangle.righthalf.fill'
  | 'tropicalstorm'
  | 'tugriksign.circle'
  | 'tugriksign.circle.fill'
  | 'tugriksign.square'
  | 'tugriksign.square.fill'
  | 'tuningfork'
  | 'turkishlirasign.circle'
  | 'turkishlirasign.circle.fill'
  | 'turkishlirasign.square'
  | 'turkishlirasign.square.fill'
  | 'tv'
  | 'tv.circle'
  | 'tv.circle.fill'
  | 'tv.fill'
  | 'tv.music.note'
  | 'tv.music.note.fill'
  | 'u.circle'
  | 'u.circle.fill'
  | 'u.square'
  | 'u.square.fill'
  | 'uiwindow.split.2x1'
  | 'umbrella'
  | 'umbrella.fill'
  | 'underline'
  | 'v.circle'
  | 'v.circle.fill'
  | 'v.square'
  | 'v.square.fill'
  | 'video'
  | 'video.badge.plus'
  | 'video.badge.plus.fill'
  | 'video.circle'
  | 'video.circle.fill'
  | 'video.fill'
  | 'video.slash'
  | 'video.slash.fill'
  | 'view.2d'
  | 'view.3d'
  | 'viewfinder'
  | 'viewfinder.circle'
  | 'viewfinder.circle.fill'
  | 'w.circle'
  | 'w.circle.fill'
  | 'w.square'
  | 'w.square.fill'
  | 'wand.and.rays'
  | 'wand.and.rays.inverse'
  | 'wand.and.stars'
  | 'wand.and.stars.inverse'
  | 'waveform'
  | 'waveform.circle'
  | 'waveform.circle.fill'
  | 'waveform.path'
  | 'waveform.path.badge.minus'
  | 'waveform.path.badge.plus'
  | 'waveform.path.ecg'
  | 'wifi'
  | 'wifi.exclamationmark'
  | 'wifi.slash'
  | 'wind'
  | 'wind.snow'
  | 'wonsign.circle'
  | 'wonsign.circle.fill'
  | 'wonsign.square'
  | 'wonsign.square.fill'
  | 'wrench'
  | 'wrench.fill'
  | 'x.circle'
  | 'x.circle.fill'
  | 'x.square'
  | 'x.square.fill'
  | 'x.squareroot'
  | 'xmark'
  | 'xmark.circle'
  | 'xmark.circle.fill'
  | 'xmark.icloud'
  | 'xmark.icloud.fill'
  | 'xmark.octagon'
  | 'xmark.octagon.fill'
  | 'xmark.rectangle'
  | 'xmark.rectangle.fill'
  | 'xmark.seal'
  | 'xmark.seal.fill'
  | 'xmark.shield'
  | 'xmark.shield.fill'
  | 'xmark.square'
  | 'xmark.square.fill'
  | 'y.circle'
  | 'y.circle.fill'
  | 'y.square'
  | 'y.square.fill'
  | 'yensign.circle'
  | 'yensign.circle.fill'
  | 'yensign.square'
  | 'yensign.square.fill'
  | 'z.circle'
  | 'z.circle.fill'
  | 'z.square'
  | 'z.square.fill'
  | 'zzz'

/**
 * @name SF Symbols 1.1
 * @description These symbols are available on the following platforms:
 * iOS v13.1+,
 * macOS v10.15+,
 * tvOS v13.0+,
 * visionOS v1.0+,
 * watchOS v6.1+
 */
export type SFSymbols1_1 =
  | SFSymbols1_0
  | 'arrow.uturn.left.circle.badge.ellipsis'
  | 'aspectratio'
  | 'aspectratio.fill'
  | 'car'
  | 'circle.grid.2x2'
  | 'circle.grid.2x2.fill'
  | 'flashlight.off.fill'
  | 'flashlight.on.fill'
  | 'flip.horizontal'
  | 'flip.horizontal.fill'
  | 'mappin.circle'
  | 'mappin.circle.fill'
  | 'paperclip.circle'
  | 'paperclip.circle.fill'
  | 'pin.circle'
  | 'pin.circle.fill'
  | 'scissors.badge.ellipsis'
  | 'studentdesk'

/**
 * @name SF Symbols 2.0
 * @description These symbols are available on the following platforms:
 * iOS v14.0+,
 * macOS v11.0+,
 * tvOS v14.0+,
 * visionOS v1.0+,
 * watchOS v7.0+
 */
export type SFSymbols2_0 =
  | SFSymbols1_1
  | '1.magnifyingglass.ar'
  | '4k.tv'
  | '4k.tv.fill'
  | 'a.book.closed'
  | 'a.book.closed.ar'
  | 'a.book.closed.fill'
  | 'a.book.closed.fill.ar'
  | 'a.book.closed.fill.he'
  | 'a.book.closed.fill.hi'
  | 'a.book.closed.fill.ja'
  | 'a.book.closed.fill.ko'
  | 'a.book.closed.fill.th'
  | 'a.book.closed.fill.zh'
  | 'a.book.closed.he'
  | 'a.book.closed.hi'
  | 'a.book.closed.ja'
  | 'a.book.closed.ko'
  | 'a.book.closed.th'
  | 'a.book.closed.zh'
  | 'a.magnify'
  | 'abc'
  | 'airplane.circle'
  | 'airplane.circle.fill'
  | 'airpod.left'
  | 'airpod.right'
  | 'airpodpro.left'
  | 'airpodpro.right'
  | 'airpods'
  | 'airpodspro'
  | 'airport.express'
  | 'airport.extreme'
  | 'airport.extreme.tower'
  | 'amplifier'
  | 'appclip'
  | 'applelogo'
  | 'applescript'
  | 'applescript.fill'
  | 'appletv'
  | 'appletv.fill'
  | 'applewatch'
  | 'applewatch.radiowaves.left.and.right'
  | 'applewatch.slash'
  | 'applewatch.watchface'
  | 'apps.ipad'
  | 'apps.ipad.landscape'
  | 'apps.iphone'
  | 'apps.iphone.badge.plus'
  | 'apps.iphone.landscape'
  | 'apps.iphone.landscape.rtl'
  | 'archivebox.circle'
  | 'archivebox.circle.fill'
  | 'arrow.backward'
  | 'arrow.backward.circle'
  | 'arrow.backward.circle.fill'
  | 'arrow.backward.square'
  | 'arrow.backward.square.fill'
  | 'arrow.clockwise.heart'
  | 'arrow.clockwise.heart.fill'
  | 'arrow.down.app'
  | 'arrow.down.app.fill'
  | 'arrow.down.backward'
  | 'arrow.down.backward.circle'
  | 'arrow.down.backward.circle.fill'
  | 'arrow.down.backward.square'
  | 'arrow.down.backward.square.fill'
  | 'arrow.down.forward'
  | 'arrow.down.forward.and.arrow.up.backward'
  | 'arrow.down.forward.and.arrow.up.backward.circle'
  | 'arrow.down.forward.and.arrow.up.backward.circle.fill'
  | 'arrow.down.forward.circle'
  | 'arrow.down.forward.circle.fill'
  | 'arrow.down.forward.square'
  | 'arrow.down.forward.square.fill'
  | 'arrow.down.heart'
  | 'arrow.down.heart.fill'
  | 'arrow.down.right.and.arrow.up.left.circle'
  | 'arrow.down.right.and.arrow.up.left.circle.fill'
  | 'arrow.forward'
  | 'arrow.forward.circle'
  | 'arrow.forward.circle.fill'
  | 'arrow.forward.square'
  | 'arrow.forward.square.fill'
  | 'arrow.left.and.right.righttriangle.left.righttriangle.right'
  | 'arrow.left.and.right.righttriangle.left.righttriangle.right.fill'
  | 'arrow.left.arrow.right'
  | 'arrow.left.arrow.right.circle'
  | 'arrow.left.arrow.right.circle.fill'
  | 'arrow.left.arrow.right.square'
  | 'arrow.left.arrow.right.square.fill'
  | 'arrow.rectanglepath'
  | 'arrow.right.doc.on.clipboard'
  | 'arrow.triangle.2.circlepath'
  | 'arrow.triangle.2.circlepath.camera'
  | 'arrow.triangle.2.circlepath.camera.fill'
  | 'arrow.triangle.2.circlepath.circle'
  | 'arrow.triangle.2.circlepath.circle.fill'
  | 'arrow.triangle.2.circlepath.doc.on.clipboard'
  | 'arrow.triangle.branch'
  | 'arrow.triangle.capsulepath'
  | 'arrow.triangle.merge'
  | 'arrow.triangle.pull'
  | 'arrow.triangle.swap'
  | 'arrow.triangle.turn.up.right.circle'
  | 'arrow.triangle.turn.up.right.circle.fill'
  | 'arrow.triangle.turn.up.right.diamond'
  | 'arrow.triangle.turn.up.right.diamond.fill'
  | 'arrow.turn.up.forward.iphone'
  | 'arrow.turn.up.forward.iphone.fill'
  | 'arrow.up.and.down.and.arrow.left.and.right'
  | 'arrow.up.and.down.righttriangle.up.fill.righttriangle.down.fill'
  | 'arrow.up.and.down.righttriangle.up.righttriangle.down'
  | 'arrow.up.and.person.rectangle.portrait'
  | 'arrow.up.and.person.rectangle.turn.left'
  | 'arrow.up.and.person.rectangle.turn.right'
  | 'arrow.up.backward'
  | 'arrow.up.backward.and.arrow.down.forward'
  | 'arrow.up.backward.and.arrow.down.forward.circle'
  | 'arrow.up.backward.and.arrow.down.forward.circle.fill'
  | 'arrow.up.backward.circle'
  | 'arrow.up.backward.circle.fill'
  | 'arrow.up.backward.square'
  | 'arrow.up.backward.square.fill'
  | 'arrow.up.doc.on.clipboard'
  | 'arrow.up.forward'
  | 'arrow.up.forward.app'
  | 'arrow.up.forward.app.fill'
  | 'arrow.up.forward.circle'
  | 'arrow.up.forward.circle.fill'
  | 'arrow.up.forward.square'
  | 'arrow.up.forward.square.fill'
  | 'arrow.up.heart'
  | 'arrow.up.heart.fill'
  | 'arrow.up.left.and.arrow.down.right.circle'
  | 'arrow.up.left.and.arrow.down.right.circle.fill'
  | 'arrow.up.left.and.down.right.and.arrow.up.right.and.down.left'
  | 'arrow.up.left.and.down.right.magnifyingglass'
  | 'arrow.up.message'
  | 'arrow.up.message.fill'
  | 'arrow.up.right.and.arrow.down.left.rectangle'
  | 'arrow.up.right.and.arrow.down.left.rectangle.fill'
  | 'arrow.uturn.backward'
  | 'arrow.uturn.backward.circle'
  | 'arrow.uturn.backward.circle.badge.ellipsis'
  | 'arrow.uturn.backward.circle.fill'
  | 'arrow.uturn.backward.square'
  | 'arrow.uturn.backward.square.fill'
  | 'arrow.uturn.forward'
  | 'arrow.uturn.forward.circle'
  | 'arrow.uturn.forward.circle.fill'
  | 'arrow.uturn.forward.square'
  | 'arrow.uturn.forward.square.fill'
  | 'arrowshape.bounce.forward'
  | 'arrowshape.bounce.forward.fill'
  | 'arrowshape.bounce.right'
  | 'arrowshape.bounce.right.fill'
  | 'arrowshape.turn.up.backward'
  | 'arrowshape.turn.up.backward.2'
  | 'arrowshape.turn.up.backward.2.circle'
  | 'arrowshape.turn.up.backward.2.circle.fill'
  | 'arrowshape.turn.up.backward.2.fill'
  | 'arrowshape.turn.up.backward.circle'
  | 'arrowshape.turn.up.backward.circle.fill'
  | 'arrowshape.turn.up.backward.fill'
  | 'arrowshape.turn.up.forward'
  | 'arrowshape.turn.up.forward.circle'
  | 'arrowshape.turn.up.forward.circle.fill'
  | 'arrowshape.turn.up.forward.fill'
  | 'arrowshape.turn.up.left.2.circle'
  | 'arrowshape.turn.up.left.2.circle.fill'
  | 'arrowshape.zigzag.forward'
  | 'arrowshape.zigzag.forward.fill'
  | 'arrowshape.zigzag.right'
  | 'arrowshape.zigzag.right.fill'
  | 'arrowtriangle.backward'
  | 'arrowtriangle.backward.circle'
  | 'arrowtriangle.backward.circle.fill'
  | 'arrowtriangle.backward.fill'
  | 'arrowtriangle.backward.square'
  | 'arrowtriangle.backward.square.fill'
  | 'arrowtriangle.forward'
  | 'arrowtriangle.forward.circle'
  | 'arrowtriangle.forward.circle.fill'
  | 'arrowtriangle.forward.fill'
  | 'arrowtriangle.forward.square'
  | 'arrowtriangle.forward.square.fill'
  | 'arrowtriangle.left.and.line.vertical.and.arrowtriangle.right'
  | 'arrowtriangle.left.fill.and.line.vertical.and.arrowtriangle.right.fill'
  | 'arrowtriangle.right.and.line.vertical.and.arrowtriangle.left'
  | 'arrowtriangle.right.fill.and.line.vertical.and.arrowtriangle.left.fill'
  | 'at.circle'
  | 'at.circle.fill'
  | 'atom'
  | 'backward.frame'
  | 'backward.frame.fill'
  | 'badge.plus.radiowaves.forward'
  | 'bag.circle'
  | 'bag.circle.fill'
  | 'banknote'
  | 'banknote.fill'
  | 'barometer'
  | 'battery.100.bolt'
  | 'battery.100.bolt.rtl'
  | 'bell.badge'
  | 'bell.badge.fill'
  | 'bell.slash.circle'
  | 'bell.slash.circle.fill'
  | 'bicycle'
  | 'bicycle.circle'
  | 'bicycle.circle.fill'
  | 'binoculars'
  | 'binoculars.fill'
  | 'bolt.car'
  | 'bolt.car.fill'
  | 'bolt.fill.batteryblock'
  | 'bolt.fill.batteryblock.fill'
  | 'bolt.heart'
  | 'bolt.heart.fill'
  | 'bolt.slash.circle'
  | 'bolt.slash.circle.fill'
  | 'bonjour'
  | 'book.closed'
  | 'book.closed.fill'
  | 'bookmark.circle'
  | 'bookmark.circle.fill'
  | 'bookmark.slash'
  | 'bookmark.slash.fill'
  | 'books.vertical'
  | 'books.vertical.fill'
  | 'building'
  | 'building.2'
  | 'building.2.crop.circle'
  | 'building.2.crop.circle.fill'
  | 'building.2.fill'
  | 'building.columns'
  | 'building.columns.fill'
  | 'building.fill'
  | 'bus'
  | 'bus.doubledecker'
  | 'bus.doubledecker.fill'
  | 'bus.fill'
  | 'calendar.badge.clock'
  | 'calendar.badge.clock.rtl'
  | 'calendar.badge.exclamationmark'
  | 'camera.aperture'
  | 'camera.badge.ellipsis'
  | 'camera.fill.badge.ellipsis'
  | 'camera.filters'
  | 'camera.metering.center.weighted'
  | 'camera.metering.center.weighted.average'
  | 'camera.metering.matrix'
  | 'camera.metering.multispot'
  | 'camera.metering.none'
  | 'camera.metering.partial'
  | 'camera.metering.spot'
  | 'camera.metering.unknown'
  | 'camera.metering.unknown.ar'
  | 'candybarphone'
  | 'capsule.portrait'
  | 'capsule.portrait.fill'
  | 'car.2'
  | 'car.2.fill'
  | 'car.circle'
  | 'car.circle.fill'
  | 'case'
  | 'case.fill'
  | 'chart.bar.doc.horizontal'
  | 'chart.bar.doc.horizontal.fill'
  | 'chart.bar.xaxis'
  | 'checkerboard.rectangle'
  | 'checkmark.icloud'
  | 'checkmark.icloud.fill'
  | 'checkmark.rectangle.portrait'
  | 'checkmark.rectangle.portrait.fill'
  | 'chevron.backward'
  | 'chevron.backward.2'
  | 'chevron.backward.circle'
  | 'chevron.backward.circle.fill'
  | 'chevron.backward.square'
  | 'chevron.backward.square.fill'
  | 'chevron.forward'
  | 'chevron.forward.2'
  | 'chevron.forward.circle'
  | 'chevron.forward.circle.fill'
  | 'chevron.forward.square'
  | 'chevron.forward.square.fill'
  | 'circle.bottomhalf.fill'
  | 'circle.circle'
  | 'circle.circle.fill'
  | 'circle.dashed'
  | 'circle.dashed.inset.fill'
  | 'circle.fill.square.fill'
  | 'circle.grid.cross'
  | 'circle.grid.cross.down.fill'
  | 'circle.grid.cross.fill'
  | 'circle.grid.cross.left.fill'
  | 'circle.grid.cross.right.fill'
  | 'circle.grid.cross.up.fill'
  | 'circle.square'
  | 'circle.tophalf.fill'
  | 'circlebadge'
  | 'circlebadge.2'
  | 'circlebadge.2.fill'
  | 'circlebadge.fill'
  | 'circles.hexagongrid'
  | 'circles.hexagongrid.fill'
  | 'circles.hexagonpath'
  | 'circles.hexagonpath.fill'
  | 'clock.arrow.circlepath'
  | 'comb'
  | 'comb.fill'
  | 'command.circle'
  | 'command.circle.fill'
  | 'command.square'
  | 'command.square.fill'
  | 'cone'
  | 'cone.fill'
  | 'contextualmenu.and.cursorarrow'
  | 'cpu'
  | 'creditcard.circle'
  | 'creditcard.circle.fill'
  | 'cross'
  | 'cross.case'
  | 'cross.case.fill'
  | 'cross.circle'
  | 'cross.circle.fill'
  | 'cross.fill'
  | 'crown'
  | 'crown.fill'
  | 'cube.transparent'
  | 'cube.transparent.fill'
  | 'curlybraces'
  | 'curlybraces.square'
  | 'curlybraces.square.fill'
  | 'cursorarrow'
  | 'cursorarrow.and.square.on.square.dashed'
  | 'cursorarrow.click'
  | 'cursorarrow.click.2'
  | 'cursorarrow.click.badge.clock'
  | 'cursorarrow.motionlines'
  | 'cursorarrow.motionlines.click'
  | 'cursorarrow.rays'
  | 'cursorarrow.square'
  | 'cylinder'
  | 'cylinder.fill'
  | 'cylinder.split.1x2'
  | 'cylinder.split.1x2.fill'
  | 'deskclock'
  | 'deskclock.fill'
  | 'dial.max'
  | 'dial.max.fill'
  | 'dial.min'
  | 'dial.min.fill'
  | 'diamond'
  | 'diamond.fill'
  | 'die.face.1'
  | 'die.face.1.fill'
  | 'die.face.2'
  | 'die.face.2.fill'
  | 'die.face.3'
  | 'die.face.3.fill'
  | 'die.face.4'
  | 'die.face.4.fill'
  | 'die.face.5'
  | 'die.face.5.fill'
  | 'die.face.6'
  | 'die.face.6.fill'
  | 'display'
  | 'display.2'
  | 'display.trianglebadge.exclamationmark'
  | 'doc.append.fill'
  | 'doc.append.fill.rtl'
  | 'doc.append.rtl'
  | 'doc.badge.ellipsis'
  | 'doc.badge.gearshape'
  | 'doc.badge.gearshape.fill'
  | 'doc.badge.plus'
  | 'doc.fill.badge.ellipsis'
  | 'doc.fill.badge.plus'
  | 'doc.plaintext.fill'
  | 'doc.richtext.ar'
  | 'doc.richtext.fill'
  | 'doc.richtext.fill.ar'
  | 'doc.richtext.fill.he'
  | 'doc.richtext.he'
  | 'doc.text.below.ecg'
  | 'doc.text.below.ecg.fill'
  | 'doc.text.fill.viewfinder'
  | 'doc.zipper'
  | 'dock.arrow.down.rectangle'
  | 'dock.arrow.up.rectangle'
  | 'dock.rectangle'
  | 'dot.arrowtriangles.up.right.down.left.circle'
  | 'dot.circle.and.cursorarrow'
  | 'dot.radiowaves.forward'
  | 'dot.squareshape'
  | 'dot.squareshape.fill'
  | 'dot.squareshape.split.2x2'
  | 'dpad'
  | 'dpad.down.fill'
  | 'dpad.fill'
  | 'dpad.left.fill'
  | 'dpad.right.fill'
  | 'dpad.up.fill'
  | 'drop'
  | 'drop.fill'
  | 'ear.badge.checkmark'
  | 'ear.fill'
  | 'ear.trianglebadge.exclamationmark'
  | 'earpods'
  | 'eject.circle'
  | 'eject.circle.fill'
  | 'ellipsis.bubble'
  | 'ellipsis.bubble.fill'
  | 'ellipsis.rectangle'
  | 'ellipsis.rectangle.fill'
  | 'envelope.arrow.triangle.branch'
  | 'envelope.arrow.triangle.branch.fill'
  | 'envelope.badge.shield.leadinghalf.fill'
  | 'envelope.fill.badge.shield.trailinghalf.fill'
  | 'esim'
  | 'esim.fill'
  | 'exclamationmark.2'
  | 'exclamationmark.3'
  | 'exclamationmark.arrow.circlepath'
  | 'exclamationmark.arrow.triangle.2.circlepath'
  | 'externaldrive'
  | 'externaldrive.badge.checkmark'
  | 'externaldrive.badge.icloud'
  | 'externaldrive.badge.minus'
  | 'externaldrive.badge.person.crop'
  | 'externaldrive.badge.plus'
  | 'externaldrive.badge.timemachine'
  | 'externaldrive.badge.wifi'
  | 'externaldrive.badge.xmark'
  | 'externaldrive.connected.to.line.below'
  | 'externaldrive.connected.to.line.below.fill'
  | 'externaldrive.fill'
  | 'externaldrive.fill.badge.checkmark'
  | 'externaldrive.fill.badge.icloud'
  | 'externaldrive.fill.badge.minus'
  | 'externaldrive.fill.badge.person.crop'
  | 'externaldrive.fill.badge.plus'
  | 'externaldrive.fill.badge.timemachine'
  | 'externaldrive.fill.badge.wifi'
  | 'externaldrive.fill.badge.xmark'
  | 'eye.circle'
  | 'eye.circle.fill'
  | 'eyebrow'
  | 'eyes'
  | 'eyes.inverse'
  | 'face.dashed'
  | 'face.dashed.fill'
  | 'face.smiling'
  | 'face.smiling.fill'
  | 'faxmachine'
  | 'fiberchannel'
  | 'figure.stand'
  | 'figure.stand.line.dotted.figure.stand'
  | 'figure.walk'
  | 'figure.walk.circle'
  | 'figure.walk.circle.fill'
  | 'figure.walk.diamond'
  | 'figure.walk.diamond.fill'
  | 'figure.wave'
  | 'figure.wave.circle'
  | 'figure.wave.circle.fill'
  | 'filemenu.and.cursorarrow'
  | 'filemenu.and.cursorarrow.rtl'
  | 'filemenu.and.selection'
  | 'flag.badge.ellipsis'
  | 'flag.badge.ellipsis.fill'
  | 'flag.slash.circle'
  | 'flag.slash.circle.fill'
  | 'flipphone'
  | 'fn'
  | 'folder.badge.gear'
  | 'folder.badge.questionmark'
  | 'folder.badge.questionmark.ar'
  | 'folder.fill.badge.gear'
  | 'folder.fill.badge.questionmark'
  | 'folder.fill.badge.questionmark.ar'
  | 'forward.frame'
  | 'forward.frame.fill'
  | 'gearshape'
  | 'gearshape.2'
  | 'gearshape.2.fill'
  | 'gearshape.fill'
  | 'gift.circle'
  | 'gift.circle.fill'
  | 'giftcard'
  | 'giftcard.fill'
  | 'graduationcap'
  | 'graduationcap.fill'
  | 'greetingcard'
  | 'greetingcard.fill'
  | 'guitars.fill'
  | 'gyroscope'
  | 'h.square.fill.on.square.fill'
  | 'h.square.on.square'
  | 'hand.point.down'
  | 'hand.point.down.fill'
  | 'hand.point.up'
  | 'hand.point.up.braille'
  | 'hand.point.up.braille.fill'
  | 'hand.point.up.fill'
  | 'hand.point.up.left'
  | 'hand.point.up.left.fill'
  | 'hand.tap'
  | 'hand.tap.fill'
  | 'hand.wave'
  | 'hand.wave.fill'
  | 'hands.clap'
  | 'hands.clap.fill'
  | 'hands.sparkles'
  | 'hands.sparkles.fill'
  | 'headphones.circle'
  | 'headphones.circle.fill'
  | 'hearingaid.ear'
  | 'heart.text.square'
  | 'heart.text.square.fill'
  | 'hifispeaker.2'
  | 'hifispeaker.2.fill'
  | 'hifispeaker.and.homepod'
  | 'hifispeaker.and.homepod.fill'
  | 'highlighter'
  | 'homekit'
  | 'homepod'
  | 'homepod.2'
  | 'homepod.2.fill'
  | 'homepod.fill'
  | 'hourglass.badge.plus'
  | 'house.circle'
  | 'house.circle.fill'
  | 'infinity'
  | 'internaldrive'
  | 'internaldrive.fill'
  | 'ipad'
  | 'ipad.homebutton'
  | 'ipad.homebutton.landscape'
  | 'ipad.landscape'
  | 'iphone'
  | 'iphone.homebutton'
  | 'iphone.homebutton.radiowaves.left.and.right'
  | 'iphone.homebutton.slash'
  | 'iphone.radiowaves.left.and.right'
  | 'iphone.slash'
  | 'ipod'
  | 'ipodshuffle.gen1'
  | 'ipodshuffle.gen2'
  | 'ipodshuffle.gen3'
  | 'ipodshuffle.gen4'
  | 'ipodtouch'
  | 'j.square.fill.on.square.fill'
  | 'j.square.on.square'
  | 'k'
  | 'key'
  | 'key.fill'
  | 'key.icloud'
  | 'key.icloud.fill'
  | 'keyboard.badge.ellipsis'
  | 'keyboard.chevron.compact.left'
  | 'keyboard.macwindow'
  | 'keyboard.onehanded.left'
  | 'keyboard.onehanded.right'
  | 'l.joystick'
  | 'l.joystick.down'
  | 'l.joystick.down.fill'
  | 'l.joystick.fill'
  | 'l.rectangle.roundedbottom'
  | 'l.rectangle.roundedbottom.fill'
  | 'l1.rectangle.roundedbottom'
  | 'l1.rectangle.roundedbottom.fill'
  | 'l2.rectangle.roundedtop'
  | 'l2.rectangle.roundedtop.fill'
  | 'ladybug'
  | 'ladybug.fill'
  | 'laptopcomputer'
  | 'laptopcomputer.and.iphone'
  | 'lasso.sparkles'
  | 'latch.2.case'
  | 'latch.2.case.fill'
  | 'lb.rectangle.roundedbottom'
  | 'lb.rectangle.roundedbottom.fill'
  | 'leaf'
  | 'leaf.arrow.triangle.circlepath'
  | 'leaf.fill'
  | 'level'
  | 'level.fill'
  | 'lifepreserver'
  | 'lifepreserver.fill'
  | 'line.3.crossed.swirl.circle'
  | 'line.3.crossed.swirl.circle.fill'
  | 'line.diagonal'
  | 'line.diagonal.arrow'
  | 'line.horizontal.2.decrease.circle'
  | 'line.horizontal.2.decrease.circle.fill'
  | 'line.horizontal.3.circle'
  | 'line.horizontal.3.circle.fill'
  | 'line.horizontal.star.fill.line.horizontal'
  | 'lineweight'
  | 'link.badge.plus'
  | 'list.and.film'
  | 'list.bullet.rectangle'
  | 'list.number.ar'
  | 'list.star'
  | 'list.triangle'
  | 'livephoto.badge.a'
  | 'location.fill.viewfinder'
  | 'location.viewfinder'
  | 'lock.doc'
  | 'lock.doc.fill'
  | 'lock.rectangle'
  | 'lock.rectangle.fill'
  | 'lock.rectangle.on.rectangle'
  | 'lock.rectangle.on.rectangle.fill'
  | 'lock.rectangle.stack'
  | 'lock.rectangle.stack.fill'
  | 'lock.square'
  | 'lock.square.fill'
  | 'lock.square.stack'
  | 'lock.square.stack.fill'
  | 'loupe'
  | 'lt.rectangle.roundedtop'
  | 'lt.rectangle.roundedtop.fill'
  | 'lungs'
  | 'lungs.fill'
  | 'macmini'
  | 'macmini.fill'
  | 'macpro.gen1'
  | 'macpro.gen2'
  | 'macpro.gen2.fill'
  | 'macpro.gen3'
  | 'macpro.gen3.server'
  | 'macwindow.badge.plus'
  | 'macwindow.on.rectangle'
  | 'macwindow.on.rectangle.rtl'
  | 'mail'
  | 'mail.and.text.magnifyingglass'
  | 'mail.and.text.magnifyingglass.rtl'
  | 'mail.fill'
  | 'mail.stack'
  | 'mail.stack.fill'
  | 'megaphone'
  | 'megaphone.fill'
  | 'memorychip'
  | 'menubar.arrow.down.rectangle'
  | 'menubar.arrow.up.rectangle'
  | 'menubar.dock.rectangle'
  | 'menubar.dock.rectangle.badge.record'
  | 'menubar.rectangle'
  | 'metronome.fill'
  | 'minus.diamond'
  | 'minus.diamond.fill'
  | 'minus.plus.batteryblock'
  | 'minus.plus.batteryblock.fill'
  | 'minus.rectangle.portrait'
  | 'minus.rectangle.portrait.fill'
  | 'mosaic'
  | 'mosaic.fill'
  | 'mount'
  | 'mount.fill'
  | 'mouth'
  | 'mouth.fill'
  | 'move.3d'
  | 'music.note.house'
  | 'music.note.house.fill'
  | 'music.quarternote.3'
  | 'mustache'
  | 'mustache.fill'
  | 'network'
  | 'newspaper'
  | 'newspaper.fill'
  | 'nose'
  | 'nose.fill'
  | 'note'
  | 'note.text'
  | 'note.text.badge.plus'
  | 'octagon'
  | 'octagon.fill'
  | 'opticaldisc'
  | 'opticaldiscdrive'
  | 'opticaldiscdrive.fill'
  | 'oval'
  | 'oval.fill'
  | 'oval.portrait'
  | 'oval.portrait.fill'
  | 'paintbrush.pointed'
  | 'paintbrush.pointed.fill'
  | 'paintpalette'
  | 'paintpalette.fill'
  | 'paperclip.badge.ellipsis'
  | 'paperplane.circle'
  | 'paperplane.circle.fill'
  | 'paragraphsign'
  | 'pc'
  | 'pencil.tip.crop.circle.badge.arrow.forward'
  | 'percent.ar'
  | 'person.2.circle'
  | 'person.2.circle.fill'
  | 'person.and.arrow.left.and.arrow.right'
  | 'person.crop.circle.badge.exclamationmark'
  | 'person.crop.circle.badge.questionmark'
  | 'person.crop.circle.badge.questionmark.ar'
  | 'person.crop.circle.fill.badge.exclamationmark'
  | 'person.crop.circle.fill.badge.questionmark'
  | 'person.crop.circle.fill.badge.questionmark.ar'
  | 'person.crop.square.fill.and.at.rectangle'
  | 'person.fill.and.arrow.left.and.arrow.right'
  | 'person.fill.badge.minus'
  | 'person.fill.badge.plus'
  | 'person.fill.checkmark'
  | 'person.fill.checkmark.rtl'
  | 'person.fill.questionmark'
  | 'person.fill.questionmark.ar'
  | 'person.fill.questionmark.rtl'
  | 'person.fill.turn.down'
  | 'person.fill.turn.left'
  | 'person.fill.turn.right'
  | 'person.fill.viewfinder'
  | 'person.fill.xmark'
  | 'person.fill.xmark.rtl'
  | 'phone.bubble.left'
  | 'phone.bubble.left.fill'
  | 'phone.connection'
  | 'phone.fill.connection'
  | 'photo.on.rectangle.angled'
  | 'pianokeys'
  | 'pianokeys.inverse'
  | 'pills'
  | 'pills.fill'
  | 'pip'
  | 'pip.enter'
  | 'pip.exit'
  | 'pip.fill'
  | 'pip.remove'
  | 'pip.swap'
  | 'placeholdertext.fill'
  | 'play.slash'
  | 'play.slash.fill'
  | 'plus.diamond'
  | 'plus.diamond.fill'
  | 'plus.message'
  | 'plus.message.fill'
  | 'plus.rectangle.fill.on.folder.fill'
  | 'plus.rectangle.on.folder'
  | 'plus.rectangle.portrait'
  | 'plus.rectangle.portrait.fill'
  | 'plus.viewfinder'
  | 'point.fill.topleft.down.curvedto.point.fill.bottomright.up'
  | 'point.topleft.down.curvedto.point.bottomright.up'
  | 'poweroff'
  | 'poweron'
  | 'powersleep'
  | 'printer.dotmatrix'
  | 'printer.dotmatrix.fill'
  | 'printer.dotmatrix.fill.and.paper.fill'
  | 'printer.fill.and.paper.fill'
  | 'puzzlepiece'
  | 'puzzlepiece.fill'
  | 'pyramid'
  | 'pyramid.fill'
  | 'questionmark.ar'
  | 'questionmark.circle.ar'
  | 'questionmark.circle.fill.ar'
  | 'questionmark.diamond.ar'
  | 'questionmark.diamond.fill.ar'
  | 'questionmark.folder'
  | 'questionmark.folder.ar'
  | 'questionmark.folder.fill'
  | 'questionmark.folder.fill.ar'
  | 'questionmark.square.ar'
  | 'questionmark.square.dashed'
  | 'questionmark.square.dashed.ar'
  | 'questionmark.square.fill.ar'
  | 'questionmark.video.ar'
  | 'questionmark.video.fill.ar'
  | 'quote.bubble.fill.rtl'
  | 'quote.bubble.rtl'
  | 'r.joystick'
  | 'r.joystick.down'
  | 'r.joystick.down.fill'
  | 'r.joystick.fill'
  | 'r.rectangle.roundedbottom'
  | 'r.rectangle.roundedbottom.fill'
  | 'r.square.fill.on.square.fill'
  | 'r.square.on.square'
  | 'r1.rectangle.roundedbottom'
  | 'r1.rectangle.roundedbottom.fill'
  | 'r2.rectangle.roundedtop'
  | 'r2.rectangle.roundedtop.fill'
  | 'radio'
  | 'radio.fill'
  | 'rb.rectangle.roundedbottom'
  | 'rb.rectangle.roundedbottom.fill'
  | 'record.circle'
  | 'record.circle.fill'
  | 'rectangle.3.offgrid.bubble.left'
  | 'rectangle.3.offgrid.bubble.left.fill'
  | 'rectangle.and.pencil.and.ellipsis'
  | 'rectangle.and.pencil.and.ellipsis.rtl'
  | 'rectangle.and.text.magnifyingglass'
  | 'rectangle.and.text.magnifyingglass.rtl'
  | 'rectangle.arrowtriangle.2.inward'
  | 'rectangle.arrowtriangle.2.outward'
  | 'rectangle.badge.minus'
  | 'rectangle.badge.plus'
  | 'rectangle.bottomthird.inset.fill'
  | 'rectangle.center.inset.fill'
  | 'rectangle.connected.to.line.below'
  | 'rectangle.dashed'
  | 'rectangle.dashed.and.paperclip'
  | 'rectangle.dashed.badge.record'
  | 'rectangle.fill.badge.minus'
  | 'rectangle.fill.badge.plus'
  | 'rectangle.fill.on.rectangle.fill.circle'
  | 'rectangle.fill.on.rectangle.fill.circle.fill'
  | 'rectangle.fill.on.rectangle.fill.slash.fill'
  | 'rectangle.inset.bottomleft.fill'
  | 'rectangle.inset.bottomright.fill'
  | 'rectangle.inset.fill'
  | 'rectangle.inset.topleft.fill'
  | 'rectangle.inset.topright.fill'
  | 'rectangle.lefthalf.fill'
  | 'rectangle.lefthalf.inset.fill'
  | 'rectangle.lefthalf.inset.fill.arrow.left'
  | 'rectangle.leftthird.inset.fill'
  | 'rectangle.on.rectangle.slash'
  | 'rectangle.portrait'
  | 'rectangle.portrait.arrowtriangle.2.inward'
  | 'rectangle.portrait.arrowtriangle.2.outward'
  | 'rectangle.portrait.fill'
  | 'rectangle.righthalf.fill'
  | 'rectangle.righthalf.inset.fill'
  | 'rectangle.righthalf.inset.fill.arrow.right'
  | 'rectangle.rightthird.inset.fill'
  | 'rectangle.roundedbottom'
  | 'rectangle.roundedbottom.fill'
  | 'rectangle.roundedtop'
  | 'rectangle.roundedtop.fill'
  | 'rectangle.slash'
  | 'rectangle.slash.fill'
  | 'rectangle.split.1x2'
  | 'rectangle.split.1x2.fill'
  | 'rectangle.split.2x1'
  | 'rectangle.split.2x1.fill'
  | 'rectangle.split.2x2'
  | 'rectangle.split.2x2.fill'
  | 'restart'
  | 'restart.circle'
  | 'rotate.3d'
  | 'rt.rectangle.roundedtop'
  | 'rt.rectangle.roundedtop.fill'
  | 'ruler'
  | 'ruler.fill'
  | 'scale.3d'
  | 'scalemass'
  | 'scalemass.fill'
  | 'scanner'
  | 'scanner.fill'
  | 'scribble.variable'
  | 'scroll'
  | 'scroll.fill'
  | 'sdcard'
  | 'sdcard.fill'
  | 'seal'
  | 'seal.fill'
  | 'server.rack'
  | 'shadow'
  | 'shekelsign.circle'
  | 'shekelsign.circle.fill'
  | 'shekelsign.square'
  | 'shekelsign.square.fill'
  | 'shield.lefthalf.fill.slash'
  | 'shippingbox'
  | 'shippingbox.fill'
  | 'sidebar.leading'
  | 'sidebar.squares.leading'
  | 'sidebar.squares.left'
  | 'sidebar.squares.right'
  | 'sidebar.squares.trailing'
  | 'sidebar.trailing'
  | 'signature.ar'
  | 'signature.he'
  | 'signpost.left'
  | 'signpost.left.fill'
  | 'signpost.right'
  | 'signpost.right.fill'
  | 'simcard'
  | 'simcard.2'
  | 'simcard.2.fill'
  | 'simcard.fill'
  | 'sleep'
  | 'slider.horizontal.below.square.fill.and.square'
  | 'slider.vertical.3'
  | 'sparkle'
  | 'sparkles.rectangle.stack'
  | 'sparkles.rectangle.stack.fill'
  | 'sparkles.square.fill.on.square'
  | 'speaker.slash.circle'
  | 'speaker.slash.circle.fill'
  | 'speaker.slash.circle.fill.rtl'
  | 'speaker.slash.circle.rtl'
  | 'speaker.wave.1'
  | 'speaker.wave.1.fill'
  | 'speaker.wave.2'
  | 'speaker.wave.2.circle'
  | 'speaker.wave.2.circle.fill'
  | 'speaker.wave.2.fill'
  | 'speaker.wave.3'
  | 'speaker.wave.3.fill'
  | 'square.2.stack.3d'
  | 'square.2.stack.3d.bottom.fill'
  | 'square.2.stack.3d.top.fill'
  | 'square.3.stack.3d'
  | 'square.3.stack.3d.bottom.fill'
  | 'square.3.stack.3d.middle.fill'
  | 'square.3.stack.3d.top.fill'
  | 'square.and.at.rectangle'
  | 'square.bottomhalf.fill'
  | 'square.circle'
  | 'square.circle.fill'
  | 'square.dashed'
  | 'square.dashed.inset.fill'
  | 'square.fill.on.square'
  | 'square.fill.text.grid.1x2'
  | 'square.grid.3x1.below.line.grid.1x2'
  | 'square.grid.3x1.fill.below.line.grid.1x2'
  | 'square.grid.3x1.folder.badge.plus'
  | 'square.grid.3x1.folder.fill.badge.plus'
  | 'square.grid.3x3'
  | 'square.grid.3x3.bottomleft.fill'
  | 'square.grid.3x3.bottommiddle.fill'
  | 'square.grid.3x3.bottomright.fill'
  | 'square.grid.3x3.fill'
  | 'square.grid.3x3.fill.square'
  | 'square.grid.3x3.middle.fill'
  | 'square.grid.3x3.middleleft.fill'
  | 'square.grid.3x3.middleright.fill'
  | 'square.grid.3x3.topleft.fill'
  | 'square.grid.3x3.topmiddle.fill'
  | 'square.grid.3x3.topright.fill'
  | 'square.on.square.dashed'
  | 'square.on.square.squareshape.controlhandles'
  | 'square.slash'
  | 'square.slash.fill'
  | 'square.split.bottomrightquarter'
  | 'square.split.bottomrightquarter.fill'
  | 'square.split.diagonal'
  | 'square.split.diagonal.2x2'
  | 'square.split.diagonal.2x2.fill'
  | 'square.split.diagonal.fill'
  | 'square.stack.3d.down.forward'
  | 'square.stack.3d.down.forward.fill'
  | 'square.stack.3d.forward.dottedline'
  | 'square.stack.3d.forward.dottedline.fill'
  | 'square.stack.3d.up.badge.a'
  | 'square.stack.3d.up.badge.a.fill'
  | 'square.tophalf.fill'
  | 'squareshape'
  | 'squareshape.controlhandles.on.squareshape.controlhandles'
  | 'squareshape.dashed.squareshape'
  | 'squareshape.fill'
  | 'squareshape.split.2x2'
  | 'squareshape.split.2x2.dotted'
  | 'squareshape.split.3x3'
  | 'squareshape.squareshape.dashed'
  | 'star.leadinghalf.fill'
  | 'star.square'
  | 'star.square.fill'
  | 'staroflife.circle'
  | 'staroflife.circle.fill'
  | 'stethoscope'
  | 'sum.ar'
  | 'swift'
  | 'switch.2'
  | 't.bubble.ar'
  | 't.bubble.fill.ar'
  | 't.bubble.fill.he'
  | 't.bubble.he'
  | 'tablecells'
  | 'tablecells.badge.ellipsis'
  | 'tablecells.badge.ellipsis.fill'
  | 'tablecells.fill'
  | 'tag.slash'
  | 'tag.slash.fill'
  | 'target'
  | 'teletype.circle'
  | 'teletype.circle.fill'
  | 'terminal'
  | 'terminal.fill'
  | 'text.and.command.macwindow'
  | 'text.badge.checkmark.rtl'
  | 'text.below.photo'
  | 'text.below.photo.fill'
  | 'text.book.closed'
  | 'text.book.closed.fill'
  | 'text.bubble.fill.rtl'
  | 'text.bubble.rtl'
  | 'text.cursor.ar'
  | 'text.cursor.he'
  | 'text.cursor.hi'
  | 'text.cursor.ja'
  | 'text.cursor.ko'
  | 'text.cursor.th'
  | 'text.cursor.zh'
  | 'text.magnifyingglass'
  | 'text.magnifyingglass.rtl'
  | 'text.quote.rtl'
  | 'text.redaction'
  | 'textbox.ar'
  | 'textbox.he'
  | 'textbox.hi'
  | 'textbox.ja'
  | 'textbox.ko'
  | 'textbox.th'
  | 'textbox.zh'
  | 'textformat.123.ar'
  | 'thermometer.sun.fill'
  | 'ticket'
  | 'ticket.fill'
  | 'timeline.selection'
  | 'timer.square'
  | 'togglepower'
  | 'touchid'
  | 'tram'
  | 'tram.circle'
  | 'tram.circle.fill'
  | 'tram.tunnel.fill'
  | 'tray.circle'
  | 'tray.circle.fill'
  | 'triangle.circle'
  | 'triangle.circle.fill'
  | 'tv.and.hifispeaker.fill'
  | 'video.badge.checkmark'
  | 'video.bubble.left'
  | 'video.bubble.left.fill'
  | 'video.fill.badge.checkmark'
  | 'video.fill.badge.plus'
  | 'wake'
  | 'wallet.pass'
  | 'wallet.pass.fill'
  | 'wave.3.backward'
  | 'wave.3.backward.circle'
  | 'wave.3.backward.circle.fill'
  | 'wave.3.forward'
  | 'wave.3.forward.circle'
  | 'wave.3.forward.circle.fill'
  | 'wave.3.left'
  | 'wave.3.left.circle'
  | 'wave.3.left.circle.fill'
  | 'wave.3.right'
  | 'wave.3.right.circle'
  | 'wave.3.right.circle.fill'
  | 'waveform.path.ecg.rectangle'
  | 'waveform.path.ecg.rectangle.fill'
  | 'wrench.and.screwdriver'
  | 'wrench.and.screwdriver.fill'
  | 'xmark.bin'
  | 'xmark.bin.circle'
  | 'xmark.bin.circle.fill'
  | 'xmark.bin.fill'
  | 'xmark.diamond'
  | 'xmark.diamond.fill'
  | 'xmark.rectangle.portrait'
  | 'xmark.rectangle.portrait.fill'
  | 'xserve'
  | 'zl.rectangle.roundedtop'
  | 'zl.rectangle.roundedtop.fill'
  | 'zr.rectangle.roundedtop'
  | 'zr.rectangle.roundedtop.fill'

/**
 * @name SF Symbols 2.1
 * @description These symbols are available on the following platforms:
 * iOS v14.2+,
 * macOS v11.0+,
 * tvOS v14.2+,
 * visionOS v1.0+,
 * watchOS v7.1+
 */
export type SFSymbols2_1 =
  | SFSymbols2_0
  | 'aqi.high'
  | 'aqi.low'
  | 'aqi.medium'
  | 'brazilianrealsign.circle'
  | 'brazilianrealsign.circle.fill'
  | 'brazilianrealsign.square'
  | 'brazilianrealsign.square.fill'
  | 'cart.circle'
  | 'cart.circle.fill'
  | 'character'
  | 'character.ar'
  | 'character.book.closed'
  | 'character.book.closed.ar'
  | 'character.book.closed.fill'
  | 'character.book.closed.fill.ar'
  | 'character.book.closed.fill.he'
  | 'character.book.closed.fill.hi'
  | 'character.book.closed.fill.ja'
  | 'character.book.closed.fill.ko'
  | 'character.book.closed.fill.th'
  | 'character.book.closed.fill.zh'
  | 'character.book.closed.he'
  | 'character.book.closed.hi'
  | 'character.book.closed.ja'
  | 'character.book.closed.ko'
  | 'character.book.closed.th'
  | 'character.book.closed.zh'
  | 'character.he'
  | 'character.hi'
  | 'character.ja'
  | 'character.ko'
  | 'character.th'
  | 'character.zh'
  | 'clock.arrow.2.circlepath'
  | 'directcurrent'
  | 'doc.text.below.ecg.fill.rtl'
  | 'doc.text.below.ecg.rtl'
  | 'exclamationmark.applewatch'
  | 'infinity.circle'
  | 'infinity.circle.fill'
  | 'ipad.badge.play'
  | 'ipad.homebutton.badge.play'
  | 'ipad.homebutton.landscape.badge.play'
  | 'ipad.landscape.badge.play'
  | 'iphone.badge.play'
  | 'iphone.homebutton.badge.play'
  | 'iphone.homebutton.landscape'
  | 'iphone.landscape'
  | 'ipodtouch.landscape'
  | 'lock.applewatch'
  | 'photo.tv'
  | 'play.tv'
  | 'play.tv.fill'
  | 'rectangle.badge.person.crop'
  | 'rectangle.fill.badge.person.crop'
  | 'rectangle.topthird.inset'
  | 'repeat.1.circle'
  | 'repeat.1.circle.fill'
  | 'repeat.circle'
  | 'repeat.circle.fill'
  | 'shield.checkerboard'
  | 'shuffle.circle'
  | 'shuffle.circle.fill'
  | 'text.below.photo.fill.rtl'
  | 'text.below.photo.rtl'
  | 'textformat.size.ar'
  | 'textformat.size.he'
  | 'textformat.size.hi'
  | 'textformat.size.ja'
  | 'textformat.size.ko'
  | 'textformat.size.larger'
  | 'textformat.size.larger.ar'
  | 'textformat.size.larger.he'
  | 'textformat.size.larger.hi'
  | 'textformat.size.larger.ja'
  | 'textformat.size.larger.ko'
  | 'textformat.size.larger.th'
  | 'textformat.size.larger.zh'
  | 'textformat.size.smaller'
  | 'textformat.size.smaller.ar'
  | 'textformat.size.smaller.he'
  | 'textformat.size.smaller.hi'
  | 'textformat.size.smaller.ja'
  | 'textformat.size.smaller.ko'
  | 'textformat.size.smaller.th'
  | 'textformat.size.smaller.zh'
  | 'textformat.size.th'
  | 'textformat.size.zh'
  | 'textformat.subscript.ar'
  | 'textformat.subscript.he'
  | 'textformat.subscript.hi'
  | 'textformat.subscript.ja'
  | 'textformat.subscript.ko'
  | 'textformat.subscript.th'
  | 'textformat.subscript.zh'
  | 'textformat.superscript.ar'
  | 'textformat.superscript.he'
  | 'textformat.superscript.hi'
  | 'textformat.superscript.ja'
  | 'textformat.superscript.ko'
  | 'textformat.superscript.th'
  | 'textformat.superscript.zh'
  | 'torus'
  | 'tv.and.mediabox'

/**
 * @name SF Symbols 2.2
 * @description These symbols are available on the following platforms:
 * iOS v14.5+,
 * macOS v11.3+,
 * tvOS v14.5+,
 * visionOS v1.0+,
 * watchOS v7.4+
 */
export type SFSymbols2_2 =
  | SFSymbols2_1
  | 'airpodsmax'
  | 'applewatch.side.right'
  | 'character.bubble'
  | 'character.bubble.ar'
  | 'character.bubble.fill'
  | 'character.bubble.fill.ar'
  | 'character.bubble.fill.he'
  | 'character.bubble.he'
  | 'character.cursor.ibeam'
  | 'character.cursor.ibeam.ar'
  | 'character.cursor.ibeam.he'
  | 'character.cursor.ibeam.hi'
  | 'character.cursor.ibeam.ja'
  | 'character.cursor.ibeam.ko'
  | 'character.cursor.ibeam.th'
  | 'character.cursor.ibeam.zh'
  | 'character.textbox'
  | 'character.textbox.ar'
  | 'character.textbox.he'
  | 'character.textbox.hi'
  | 'character.textbox.ja'
  | 'character.textbox.ko'
  | 'character.textbox.th'
  | 'character.textbox.zh'
  | 'hifispeaker.and.homepodmini'
  | 'hifispeaker.and.homepodmini.fill'
  | 'homepod.and.homepodmini'
  | 'homepod.and.homepodmini.fill'
  | 'homepodmini'
  | 'homepodmini.2'
  | 'homepodmini.2.fill'
  | 'homepodmini.fill'
  | 'rectangle.topthird.inset.fill'

/**
 * @name SF Symbols 3.0
 * @description These symbols are available on the following platforms:
 * iOS v15.0+,
 * macOS v12.0+,
 * tvOS v15.0+,
 * visionOS v1.0+,
 * watchOS v8.0+
 */
export type SFSymbols3_0 =
  | SFSymbols2_2
  | '1.magnifyingglass.hi'
  | '123.rectangle'
  | '123.rectangle.ar'
  | '123.rectangle.fill'
  | '123.rectangle.fill.ar'
  | '123.rectangle.fill.hi'
  | '123.rectangle.hi'
  | 'airplane.arrival'
  | 'airplane.departure'
  | 'airplayaudio.badge.exclamationmark'
  | 'airplayaudio.circle'
  | 'airplayaudio.circle.fill'
  | 'airplayvideo.badge.exclamationmark'
  | 'airplayvideo.circle'
  | 'airplayvideo.circle.fill'
  | 'airpods.chargingcase'
  | 'airpods.chargingcase.fill'
  | 'airpods.chargingcase.wireless'
  | 'airpods.chargingcase.wireless.fill'
  | 'airpodspro.chargingcase.wireless'
  | 'airpodspro.chargingcase.wireless.fill'
  | 'airtag'
  | 'airtag.fill'
  | 'airtag.radiowaves.forward'
  | 'airtag.radiowaves.forward.fill'
  | 'airtag.radiowaves.forward.fill.rtl'
  | 'airtag.radiowaves.forward.rtl'
  | 'align.horizontal.center'
  | 'align.horizontal.center.fill'
  | 'align.horizontal.left'
  | 'align.horizontal.left.fill'
  | 'align.horizontal.right'
  | 'align.horizontal.right.fill'
  | 'align.vertical.bottom'
  | 'align.vertical.bottom.fill'
  | 'align.vertical.center'
  | 'align.vertical.center.fill'
  | 'align.vertical.top'
  | 'align.vertical.top.fill'
  | 'allergens'
  | 'alternatingcurrent'
  | 'antenna.radiowaves.left.and.right.circle'
  | 'antenna.radiowaves.left.and.right.circle.fill'
  | 'antenna.radiowaves.left.and.right.slash'
  | 'app.badge.checkmark'
  | 'app.badge.checkmark.fill'
  | 'app.connected.to.app.below.fill'
  | 'app.dashed'
  | 'applepencil'
  | 'appletvremote.gen1'
  | 'appletvremote.gen1.fill'
  | 'appletvremote.gen2'
  | 'appletvremote.gen2.fill'
  | 'appletvremote.gen3'
  | 'appletvremote.gen3.fill'
  | 'appletvremote.gen4'
  | 'appletvremote.gen4.fill'
  | 'applewatch.case.inset.filled'
  | 'arkit.badge.xmark'
  | 'arrow.backward.to.line'
  | 'arrow.backward.to.line.circle'
  | 'arrow.backward.to.line.circle.fill'
  | 'arrow.down.to.line.circle'
  | 'arrow.down.to.line.circle.fill'
  | 'arrow.down.to.line.compact'
  | 'arrow.forward.to.line'
  | 'arrow.forward.to.line.circle'
  | 'arrow.forward.to.line.circle.fill'
  | 'arrow.left.to.line.circle'
  | 'arrow.left.to.line.circle.fill'
  | 'arrow.left.to.line.compact'
  | 'arrow.right.to.line.circle'
  | 'arrow.right.to.line.circle.fill'
  | 'arrow.right.to.line.compact'
  | 'arrow.up.and.down.righttriangle.up.righttriangle.down.fill'
  | 'arrow.up.to.line.circle'
  | 'arrow.up.to.line.circle.fill'
  | 'arrow.up.to.line.compact'
  | 'arrowtriangle.left.and.line.vertical.and.arrowtriangle.right.fill'
  | 'arrowtriangle.right.and.line.vertical.and.arrowtriangle.left.fill'
  | 'asterisk'
  | 'backward.circle'
  | 'backward.circle.fill'
  | 'battery.50'
  | 'battery.75'
  | 'beats.earphones'
  | 'beats.headphones'
  | 'beats.powerbeats'
  | 'beats.powerbeats3'
  | 'beats.powerbeatspro'
  | 'beats.powerbeatspro.chargingcase'
  | 'beats.powerbeatspro.chargingcase.fill'
  | 'beats.powerbeatspro.left'
  | 'beats.powerbeatspro.right'
  | 'beats.studiobud.left'
  | 'beats.studiobud.right'
  | 'beats.studiobuds'
  | 'beats.studiobuds.chargingcase'
  | 'beats.studiobuds.chargingcase.fill'
  | 'bed.double.circle'
  | 'bed.double.circle.fill'
  | 'bell.and.waveform'
  | 'bell.and.waveform.fill'
  | 'bell.badge.circle'
  | 'bell.badge.circle.fill'
  | 'bell.square'
  | 'bell.square.fill'
  | 'bolt.batteryblock'
  | 'bolt.batteryblock.fill'
  | 'bolt.car.circle'
  | 'bolt.car.circle.fill'
  | 'bolt.shield'
  | 'bolt.shield.fill'
  | 'bolt.square'
  | 'bolt.square.fill'
  | 'book.closed.circle'
  | 'book.closed.circle.fill'
  | 'bookmark.square'
  | 'bookmark.square.fill'
  | 'books.vertical.circle'
  | 'books.vertical.circle.fill'
  | 'brain'
  | 'brain.head.profile'
  | 'briefcase.circle'
  | 'briefcase.circle.fill'
  | 'bubble.left.and.exclamationmark.bubble.right'
  | 'bubble.left.and.exclamationmark.bubble.right.fill'
  | 'bubble.left.circle'
  | 'bubble.left.circle.fill'
  | 'bubble.right.circle'
  | 'bubble.right.circle.fill'
  | 'building.columns.circle'
  | 'building.columns.circle.fill'
  | 'cable.connector'
  | 'cable.connector.horizontal'
  | 'cablecar'
  | 'cablecar.fill'
  | 'calendar.day.timeline.leading'
  | 'calendar.day.timeline.left'
  | 'calendar.day.timeline.right'
  | 'calendar.day.timeline.trailing'
  | 'camera.shutter.button'
  | 'camera.shutter.button.fill'
  | 'capsule.bottomhalf.filled'
  | 'capsule.inset.filled'
  | 'capsule.lefthalf.filled'
  | 'capsule.portrait.bottomhalf.filled'
  | 'capsule.portrait.inset.filled'
  | 'capsule.portrait.lefthalf.filled'
  | 'capsule.portrait.righthalf.filled'
  | 'capsule.portrait.tophalf.filled'
  | 'capsule.righthalf.filled'
  | 'capsule.tophalf.filled'
  | 'car.ferry'
  | 'car.ferry.fill'
  | 'character.bubble.fill.hi'
  | 'character.bubble.fill.ja'
  | 'character.bubble.fill.ko'
  | 'character.bubble.fill.th'
  | 'character.bubble.fill.zh'
  | 'character.bubble.hi'
  | 'character.bubble.ja'
  | 'character.bubble.ko'
  | 'character.bubble.th'
  | 'character.bubble.zh'
  | 'chart.line.uptrend.xyaxis'
  | 'chart.line.uptrend.xyaxis.circle'
  | 'chart.line.uptrend.xyaxis.circle.fill'
  | 'chart.xyaxis.line'
  | 'checkerboard.shield'
  | 'checklist'
  | 'checklist.rtl'
  | 'checkmark.bubble'
  | 'checkmark.bubble.fill'
  | 'checkmark.circle.trianglebadge.exclamationmark'
  | 'checkmark.diamond'
  | 'checkmark.diamond.fill'
  | 'chevron.left.forwardslash.chevron.right'
  | 'circle.and.line.horizontal'
  | 'circle.and.line.horizontal.fill'
  | 'circle.bottomhalf.filled'
  | 'circle.dashed.inset.filled'
  | 'circle.dotted'
  | 'circle.grid.2x1'
  | 'circle.grid.2x1.fill'
  | 'circle.grid.2x1.left.filled'
  | 'circle.grid.2x1.right.filled'
  | 'circle.grid.3x3.circle'
  | 'circle.grid.3x3.circle.fill'
  | 'circle.grid.cross.down.filled'
  | 'circle.grid.cross.left.filled'
  | 'circle.grid.cross.right.filled'
  | 'circle.grid.cross.up.filled'
  | 'circle.hexagongrid'
  | 'circle.hexagongrid.circle'
  | 'circle.hexagongrid.circle.fill'
  | 'circle.hexagongrid.fill'
  | 'circle.hexagonpath'
  | 'circle.hexagonpath.fill'
  | 'circle.inset.filled'
  | 'circle.lefthalf.filled'
  | 'circle.righthalf.filled'
  | 'circle.slash'
  | 'circle.slash.fill'
  | 'circle.square.fill'
  | 'circle.tophalf.filled'
  | 'clock.badge.checkmark'
  | 'clock.badge.checkmark.fill'
  | 'clock.badge.exclamationmark'
  | 'clock.badge.exclamationmark.fill'
  | 'clock.circle'
  | 'clock.circle.fill'
  | 'computermouse'
  | 'computermouse.fill'
  | 'cpu.fill'
  | 'creditcard.and.123'
  | 'creditcard.trianglebadge.exclamationmark'
  | 'cross.vial'
  | 'cross.vial.fill'
  | 'cup.and.saucer'
  | 'cup.and.saucer.fill'
  | 'delete.backward'
  | 'delete.backward.fill'
  | 'delete.forward'
  | 'delete.forward.fill'
  | 'desktopcomputer.and.arrow.down'
  | 'desktopcomputer.trianglebadge.exclamationmark'
  | 'diamond.bottomhalf.filled'
  | 'diamond.circle'
  | 'diamond.circle.fill'
  | 'diamond.inset.filled'
  | 'diamond.lefthalf.filled'
  | 'diamond.righthalf.filled'
  | 'diamond.tophalf.filled'
  | 'dice'
  | 'dice.fill'
  | 'digitalcrown.arrow.clockwise'
  | 'digitalcrown.arrow.clockwise.fill'
  | 'digitalcrown.arrow.counterclockwise'
  | 'digitalcrown.arrow.counterclockwise.fill'
  | 'digitalcrown.horizontal.arrow.clockwise'
  | 'digitalcrown.horizontal.arrow.clockwise.fill'
  | 'digitalcrown.horizontal.arrow.counterclockwise'
  | 'digitalcrown.horizontal.arrow.counterclockwise.fill'
  | 'digitalcrown.horizontal.press'
  | 'digitalcrown.horizontal.press.fill'
  | 'digitalcrown.press'
  | 'digitalcrown.press.fill'
  | 'display.and.arrow.down'
  | 'doc.richtext.fill.hi'
  | 'doc.richtext.fill.ja'
  | 'doc.richtext.fill.ko'
  | 'doc.richtext.fill.th'
  | 'doc.richtext.fill.zh'
  | 'doc.richtext.hi'
  | 'doc.richtext.ja'
  | 'doc.richtext.ko'
  | 'doc.richtext.th'
  | 'doc.richtext.zh'
  | 'doc.text.image'
  | 'doc.text.image.fill'
  | 'doc.viewfinder'
  | 'doc.viewfinder.fill'
  | 'dot.circle.and.hand.point.up.left.fill'
  | 'dot.circle.viewfinder'
  | 'dot.radiowaves.up.forward'
  | 'dot.viewfinder'
  | 'dpad.down.filled'
  | 'dpad.left.filled'
  | 'dpad.right.filled'
  | 'dpad.up.filled'
  | 'drop.circle'
  | 'drop.circle.fill'
  | 'ear.and.waveform'
  | 'earbuds'
  | 'earbuds.case'
  | 'earbuds.case.fill'
  | 'ellipsis.curlybraces'
  | 'ellipsis.vertical.bubble'
  | 'ellipsis.vertical.bubble.fill'
  | 'envelope.badge.shield.half.filled'
  | 'envelope.badge.shield.half.filled.fill'
  | 'exclamationmark.bubble.circle'
  | 'exclamationmark.bubble.circle.fill'
  | 'eye.slash.circle'
  | 'eye.slash.circle.fill'
  | 'eye.square'
  | 'eye.square.fill'
  | 'eye.trianglebadge.exclamationmark'
  | 'eye.trianglebadge.exclamationmark.fill'
  | 'facemask'
  | 'facemask.fill'
  | 'fanblades'
  | 'fanblades.fill'
  | 'ferry'
  | 'ferry.fill'
  | 'fibrechannel'
  | 'figure.roll'
  | 'film.circle'
  | 'film.circle.fill'
  | 'flag.2.crossed'
  | 'flag.2.crossed.fill'
  | 'flag.and.flag.filled.crossed'
  | 'flag.filled.and.flag.crossed'
  | 'flag.square'
  | 'flag.square.fill'
  | 'flame.circle'
  | 'flame.circle.fill'
  | 'folder.badge.gearshape'
  | 'folder.fill.badge.gearshape'
  | 'fork.knife'
  | 'fork.knife.circle'
  | 'fork.knife.circle.fill'
  | 'forward.circle'
  | 'forward.circle.fill'
  | 'fuelpump'
  | 'fuelpump.circle'
  | 'fuelpump.circle.fill'
  | 'fuelpump.fill'
  | 'gear.badge.checkmark'
  | 'gear.badge.questionmark'
  | 'gear.badge.xmark'
  | 'gear.circle'
  | 'gear.circle.fill'
  | 'gearshape.circle'
  | 'gearshape.circle.fill'
  | 'globe.americas'
  | 'globe.americas.fill'
  | 'globe.asia.australia'
  | 'globe.asia.australia.fill'
  | 'globe.badge.chevron.backward'
  | 'globe.europe.africa'
  | 'globe.europe.africa.fill'
  | 'gobackward.5'
  | 'gobackward.5.ar'
  | 'gobackward.5.hi'
  | 'goforward.5'
  | 'goforward.5.ar'
  | 'goforward.5.hi'
  | 'graduationcap.circle'
  | 'graduationcap.circle.fill'
  | 'h.square.on.square.fill'
  | 'hammer.circle'
  | 'hammer.circle.fill'
  | 'hand.raised.circle'
  | 'hand.raised.circle.fill'
  | 'hand.raised.square'
  | 'hand.raised.square.fill'
  | 'hand.raised.square.on.square'
  | 'hand.raised.square.on.square.fill'
  | 'hand.thumbsdown.circle'
  | 'hand.thumbsdown.circle.fill'
  | 'hand.thumbsup.circle'
  | 'hand.thumbsup.circle.fill'
  | 'hearingdevice.ear'
  | 'heart.rectangle'
  | 'heart.rectangle.fill'
  | 'heart.square'
  | 'heart.square.fill'
  | 'hexagon.bottomhalf.filled'
  | 'hexagon.lefthalf.filled'
  | 'hexagon.righthalf.filled'
  | 'hexagon.tophalf.filled'
  | 'hifispeaker.and.appletv'
  | 'hifispeaker.and.appletv.fill'
  | 'homepod.and.appletv'
  | 'homepod.and.appletv.fill'
  | 'homepodmini.and.appletv'
  | 'homepodmini.and.appletv.fill'
  | 'hourglass.bottomhalf.filled'
  | 'hourglass.circle'
  | 'hourglass.circle.fill'
  | 'hourglass.tophalf.filled'
  | 'humidity'
  | 'humidity.fill'
  | 'icloud.square'
  | 'icloud.square.fill'
  | 'ipad.and.arrow.forward'
  | 'ipad.and.iphone'
  | 'ipad.rear.camera'
  | 'iphone.and.arrow.forward'
  | 'iphone.circle'
  | 'iphone.circle.fill'
  | 'iphone.homebutton.circle'
  | 'iphone.homebutton.circle.fill'
  | 'iphone.homebutton.radiowaves.left.and.right.circle'
  | 'iphone.homebutton.radiowaves.left.and.right.circle.fill'
  | 'iphone.homebutton.slash.circle'
  | 'iphone.homebutton.slash.circle.fill'
  | 'iphone.radiowaves.left.and.right.circle'
  | 'iphone.radiowaves.left.and.right.circle.fill'
  | 'iphone.rear.camera'
  | 'iphone.slash.circle'
  | 'iphone.slash.circle.fill'
  | 'iphone.smartbatterycase.gen1'
  | 'iphone.smartbatterycase.gen2'
  | 'ipodtouch.slash'
  | 'ivfluid.bag'
  | 'ivfluid.bag.fill'
  | 'j.square.on.square.fill'
  | 'keyboard.fill'
  | 'l.joystick.press.down'
  | 'l.joystick.press.down.fill'
  | 'l.joystick.tilt.down'
  | 'l.joystick.tilt.down.fill'
  | 'l.joystick.tilt.left'
  | 'l.joystick.tilt.left.fill'
  | 'l.joystick.tilt.right'
  | 'l.joystick.tilt.right.fill'
  | 'l.joystick.tilt.up'
  | 'l.joystick.tilt.up.fill'
  | 'lanyardcard'
  | 'lanyardcard.fill'
  | 'laptopcomputer.and.arrow.down'
  | 'laptopcomputer.trianglebadge.exclamationmark'
  | 'lasso.and.sparkles'
  | 'leaf.circle'
  | 'leaf.circle.fill'
  | 'lightbulb.circle'
  | 'lightbulb.circle.fill'
  | 'line.2.horizontal.decrease.circle'
  | 'line.2.horizontal.decrease.circle.fill'
  | 'line.3.horizontal'
  | 'line.3.horizontal.circle'
  | 'line.3.horizontal.circle.fill'
  | 'line.3.horizontal.decrease'
  | 'line.3.horizontal.decrease.circle'
  | 'line.3.horizontal.decrease.circle.fill'
  | 'lines.measurement.horizontal'
  | 'list.bullet.circle'
  | 'list.bullet.circle.fill'
  | 'list.bullet.rectangle.fill'
  | 'list.bullet.rectangle.portrait'
  | 'list.bullet.rectangle.portrait.fill'
  | 'list.dash.header.rectangle'
  | 'list.number.hi'
  | 'location.magnifyingglass'
  | 'location.north.circle'
  | 'location.north.circle.fill'
  | 'location.square'
  | 'location.square.fill'
  | 'lock.desktopcomputer'
  | 'lock.display'
  | 'lock.ipad'
  | 'lock.iphone'
  | 'lock.laptopcomputer'
  | 'lock.open.applewatch'
  | 'lock.open.desktopcomputer'
  | 'lock.open.display'
  | 'lock.open.ipad'
  | 'lock.open.iphone'
  | 'lock.open.laptopcomputer'
  | 'logo.playstation'
  | 'logo.xbox'
  | 'macpro.gen1.fill'
  | 'macpro.gen3.fill'
  | 'magazine'
  | 'magazine.fill'
  | 'magicmouse'
  | 'magicmouse.fill'
  | 'magsafe.batterypack'
  | 'magsafe.batterypack.fill'
  | 'map.circle'
  | 'map.circle.fill'
  | 'mappin.slash.circle'
  | 'mappin.slash.circle.fill'
  | 'mappin.square'
  | 'mappin.square.fill'
  | 'mediastick'
  | 'memorychip.fill'
  | 'menucard'
  | 'menucard.fill'
  | 'message.and.waveform'
  | 'message.and.waveform.fill'
  | 'mic.badge.plus'
  | 'mic.fill.badge.plus'
  | 'mic.slash.circle'
  | 'mic.slash.circle.fill'
  | 'mic.square'
  | 'mic.square.fill'
  | 'minus.forwardslash.plus'
  | 'music.mic.circle'
  | 'music.mic.circle.fill'
  | 'music.note.tv'
  | 'music.note.tv.fill'
  | 'network.badge.shield.half.filled'
  | 'newspaper.circle'
  | 'newspaper.circle.fill'
  | 'octagon.bottomhalf.filled'
  | 'octagon.lefthalf.filled'
  | 'octagon.righthalf.filled'
  | 'octagon.tophalf.filled'
  | 'oval.bottomhalf.filled'
  | 'oval.inset.filled'
  | 'oval.lefthalf.filled'
  | 'oval.portrait.bottomhalf.filled'
  | 'oval.portrait.inset.filled'
  | 'oval.portrait.lefthalf.filled'
  | 'oval.portrait.righthalf.filled'
  | 'oval.portrait.tophalf.filled'
  | 'oval.righthalf.filled'
  | 'oval.tophalf.filled'
  | 'parentheses'
  | 'parkingsign'
  | 'parkingsign.circle'
  | 'parkingsign.circle.fill'
  | 'pawprint'
  | 'pawprint.circle'
  | 'pawprint.circle.fill'
  | 'pawprint.fill'
  | 'peacesign'
  | 'pentagon'
  | 'pentagon.bottomhalf.filled'
  | 'pentagon.fill'
  | 'pentagon.lefthalf.filled'
  | 'pentagon.righthalf.filled'
  | 'pentagon.tophalf.filled'
  | 'person.2.crop.square.stack'
  | 'person.2.crop.square.stack.fill'
  | 'person.2.wave.2'
  | 'person.2.wave.2.fill'
  | 'person.3.sequence'
  | 'person.3.sequence.fill'
  | 'person.badge.clock'
  | 'person.badge.clock.fill'
  | 'person.crop.artframe'
  | 'person.crop.circle.badge'
  | 'person.crop.circle.badge.clock'
  | 'person.crop.circle.badge.clock.fill'
  | 'person.crop.circle.badge.exclamationmark.fill'
  | 'person.crop.circle.badge.fill'
  | 'person.crop.circle.badge.moon'
  | 'person.crop.circle.badge.moon.fill'
  | 'person.crop.circle.badge.questionmark.fill'
  | 'person.crop.circle.badge.questionmark.fill.ar'
  | 'person.crop.rectangle.stack'
  | 'person.crop.rectangle.stack.fill'
  | 'person.crop.square.filled.and.at.rectangle'
  | 'person.crop.square.filled.and.at.rectangle.fill'
  | 'person.text.rectangle'
  | 'person.text.rectangle.fill'
  | 'person.wave.2'
  | 'person.wave.2.fill'
  | 'personalhotspot.circle'
  | 'personalhotspot.circle.fill'
  | 'phone.and.waveform'
  | 'phone.and.waveform.fill'
  | 'photo.artframe'
  | 'photo.circle'
  | 'photo.circle.fill'
  | 'pills.circle'
  | 'pills.circle.fill'
  | 'pin.square'
  | 'pin.square.fill'
  | 'platter.2.filled.ipad'
  | 'platter.2.filled.ipad.landscape'
  | 'platter.2.filled.iphone'
  | 'platter.2.filled.iphone.landscape'
  | 'platter.bottom.applewatch.case'
  | 'platter.filled.bottom.applewatch.case'
  | 'platter.filled.top.applewatch.case'
  | 'platter.top.applewatch.case'
  | 'play.rectangle.on.rectangle'
  | 'play.rectangle.on.rectangle.circle'
  | 'play.rectangle.on.rectangle.circle.fill'
  | 'play.rectangle.on.rectangle.fill'
  | 'play.square'
  | 'play.square.fill'
  | 'plus.forwardslash.minus'
  | 'plus.rectangle.on.folder.fill'
  | 'plus.square.dashed'
  | 'point.3.connected.trianglepath.dotted'
  | 'point.3.filled.connected.trianglepath.dotted'
  | 'point.filled.topleft.down.curvedto.point.bottomright.up'
  | 'point.topleft.down.curvedto.point.bottomright.up.fill'
  | 'point.topleft.down.curvedto.point.filled.bottomright.up'
  | 'power.circle'
  | 'power.circle.fill'
  | 'power.dotted'
  | 'powerplug'
  | 'powerplug.fill'
  | 'printer.dotmatrix.filled.and.paper'
  | 'printer.filled.and.paper'
  | 'puzzlepiece.extension'
  | 'puzzlepiece.extension.fill'
  | 'questionmark.app'
  | 'questionmark.app.ar'
  | 'questionmark.app.dashed'
  | 'questionmark.app.dashed.ar'
  | 'questionmark.app.fill'
  | 'questionmark.app.fill.ar'
  | 'quote.closing'
  | 'quote.opening'
  | 'r.joystick.press.down'
  | 'r.joystick.press.down.fill'
  | 'r.joystick.tilt.down'
  | 'r.joystick.tilt.down.fill'
  | 'r.joystick.tilt.left'
  | 'r.joystick.tilt.left.fill'
  | 'r.joystick.tilt.right'
  | 'r.joystick.tilt.right.fill'
  | 'r.joystick.tilt.up'
  | 'r.joystick.tilt.up.fill'
  | 'r.square.on.square.fill'
  | 'rectangle.2.swap'
  | 'rectangle.3.group'
  | 'rectangle.3.group.bubble.left'
  | 'rectangle.3.group.bubble.left.fill'
  | 'rectangle.3.group.fill'
  | 'rectangle.and.hand.point.up.left'
  | 'rectangle.and.hand.point.up.left.fill'
  | 'rectangle.and.hand.point.up.left.filled'
  | 'rectangle.bottomhalf.filled'
  | 'rectangle.bottomhalf.inset.filled'
  | 'rectangle.bottomthird.inset.filled'
  | 'rectangle.center.inset.filled'
  | 'rectangle.center.inset.filled.badge.plus'
  | 'rectangle.filled.and.hand.point.up.left'
  | 'rectangle.inset.bottomleading.filled'
  | 'rectangle.inset.bottomleft.filled'
  | 'rectangle.inset.bottomright.filled'
  | 'rectangle.inset.bottomtrailing.filled'
  | 'rectangle.inset.filled'
  | 'rectangle.inset.filled.and.person.filled'
  | 'rectangle.inset.filled.on.rectangle'
  | 'rectangle.inset.topleading.filled'
  | 'rectangle.inset.topleft.filled'
  | 'rectangle.inset.topright.filled'
  | 'rectangle.inset.toptrailing.filled'
  | 'rectangle.leadinghalf.inset.filled'
  | 'rectangle.leadinghalf.inset.filled.arrow.leading'
  | 'rectangle.leadingthird.inset.filled'
  | 'rectangle.lefthalf.filled'
  | 'rectangle.lefthalf.inset.filled'
  | 'rectangle.lefthalf.inset.filled.arrow.left'
  | 'rectangle.leftthird.inset.filled'
  | 'rectangle.on.rectangle.circle'
  | 'rectangle.on.rectangle.circle.fill'
  | 'rectangle.on.rectangle.slash.circle'
  | 'rectangle.on.rectangle.slash.circle.fill'
  | 'rectangle.on.rectangle.slash.fill'
  | 'rectangle.on.rectangle.square'
  | 'rectangle.on.rectangle.square.fill'
  | 'rectangle.portrait.and.arrow.right'
  | 'rectangle.portrait.and.arrow.right.fill'
  | 'rectangle.portrait.bottomhalf.filled'
  | 'rectangle.portrait.bottomhalf.inset.filled'
  | 'rectangle.portrait.bottomleading.inset.filled'
  | 'rectangle.portrait.bottomleft.inset.filled'
  | 'rectangle.portrait.bottomright.inset.filled'
  | 'rectangle.portrait.bottomthird.inset.filled'
  | 'rectangle.portrait.bottomtrailing.inset.filled'
  | 'rectangle.portrait.center.inset.filled'
  | 'rectangle.portrait.inset.filled'
  | 'rectangle.portrait.leadinghalf.inset.filled'
  | 'rectangle.portrait.leadingthird.inset.filled'
  | 'rectangle.portrait.lefthalf.filled'
  | 'rectangle.portrait.lefthalf.inset.filled'
  | 'rectangle.portrait.leftthird.inset.filled'
  | 'rectangle.portrait.on.rectangle.portrait'
  | 'rectangle.portrait.on.rectangle.portrait.fill'
  | 'rectangle.portrait.on.rectangle.portrait.slash'
  | 'rectangle.portrait.on.rectangle.portrait.slash.fill'
  | 'rectangle.portrait.righthalf.filled'
  | 'rectangle.portrait.righthalf.inset.filled'
  | 'rectangle.portrait.rightthird.inset.filled'
  | 'rectangle.portrait.slash'
  | 'rectangle.portrait.slash.fill'
  | 'rectangle.portrait.split.2x1'
  | 'rectangle.portrait.split.2x1.fill'
  | 'rectangle.portrait.split.2x1.slash'
  | 'rectangle.portrait.split.2x1.slash.fill'
  | 'rectangle.portrait.tophalf.filled'
  | 'rectangle.portrait.tophalf.inset.filled'
  | 'rectangle.portrait.topleading.inset.filled'
  | 'rectangle.portrait.topleft.inset.filled'
  | 'rectangle.portrait.topright.inset.filled'
  | 'rectangle.portrait.topthird.inset.filled'
  | 'rectangle.portrait.toptrailing.inset.filled'
  | 'rectangle.portrait.trailinghalf.inset.filled'
  | 'rectangle.portrait.trailingthird.inset.filled'
  | 'rectangle.righthalf.filled'
  | 'rectangle.righthalf.inset.filled'
  | 'rectangle.righthalf.inset.filled.arrow.right'
  | 'rectangle.rightthird.inset.filled'
  | 'rectangle.split.2x1.slash'
  | 'rectangle.split.2x1.slash.fill'
  | 'rectangle.stack.badge.person.crop.fill'
  | 'rectangle.stack.badge.play'
  | 'rectangle.stack.badge.play.fill'
  | 'rectangle.tophalf.filled'
  | 'rectangle.tophalf.inset.filled'
  | 'rectangle.topthird.inset.filled'
  | 'rectangle.trailinghalf.inset.filled'
  | 'rectangle.trailinghalf.inset.filled.arrow.trailing'
  | 'rectangle.trailingthird.inset.filled'
  | 'repeat.1.ar'
  | 'repeat.1.circle.ar'
  | 'repeat.1.circle.fill.ar'
  | 'repeat.1.circle.fill.hi'
  | 'repeat.1.circle.hi'
  | 'repeat.1.hi'
  | 'restart.circle.fill'
  | 'return.left'
  | 'return.right'
  | 'scissors.circle'
  | 'scissors.circle.fill'
  | 'scooter'
  | 'screwdriver'
  | 'screwdriver.fill'
  | 'sensor.tag.radiowaves.forward'
  | 'sensor.tag.radiowaves.forward.fill'
  | 'shareplay'
  | 'shareplay.slash'
  | 'shield.lefthalf.filled'
  | 'shield.lefthalf.filled.slash'
  | 'shield.righthalf.filled'
  | 'shippingbox.circle'
  | 'shippingbox.circle.fill'
  | 'signature.ja'
  | 'signature.th'
  | 'signature.zh'
  | 'sleep.circle'
  | 'sleep.circle.fill'
  | 'slider.horizontal.2.rectangle.and.arrow.triangle.2.circlepath'
  | 'slider.horizontal.below.square.filled.and.square'
  | 'smallcircle.filled.circle'
  | 'smallcircle.filled.circle.fill'
  | 'snowflake'
  | 'snowflake.circle'
  | 'snowflake.circle.fill'
  | 'sparkle.magnifyingglass'
  | 'sparkles.square.filled.on.square'
  | 'sparkles.tv'
  | 'sparkles.tv.fill'
  | 'speaker.badge.exclamationmark'
  | 'speaker.badge.exclamationmark.fill'
  | 'speaker.circle'
  | 'speaker.circle.fill'
  | 'square.2.stack.3d.bottom.filled'
  | 'square.2.stack.3d.top.filled'
  | 'square.3.stack.3d.bottom.filled'
  | 'square.3.stack.3d.middle.filled'
  | 'square.3.stack.3d.top.filled'
  | 'square.and.arrow.up.circle'
  | 'square.and.arrow.up.circle.fill'
  | 'square.and.arrow.up.trianglebadge.exclamationmark'
  | 'square.and.at.rectangle.fill'
  | 'square.and.line.vertical.and.square.filled'
  | 'square.bottomhalf.filled'
  | 'square.dashed.inset.filled'
  | 'square.fill.and.line.vertical.and.square.fill'
  | 'square.filled.and.line.vertical.and.square'
  | 'square.filled.on.square'
  | 'square.grid.3x1.below.line.grid.1x2.fill'
  | 'square.grid.3x3.bottomleft.filled'
  | 'square.grid.3x3.bottommiddle.filled'
  | 'square.grid.3x3.bottomright.filled'
  | 'square.grid.3x3.middle.filled'
  | 'square.grid.3x3.middleleft.filled'
  | 'square.grid.3x3.middleright.filled'
  | 'square.grid.3x3.square'
  | 'square.grid.3x3.topleft.filled'
  | 'square.grid.3x3.topmiddle.filled'
  | 'square.grid.3x3.topright.filled'
  | 'square.inset.filled'
  | 'square.lefthalf.filled'
  | 'square.righthalf.filled'
  | 'square.text.square'
  | 'square.text.square.fill'
  | 'square.tophalf.filled'
  | 'star.bubble'
  | 'star.bubble.fill'
  | 'star.leadinghalf.filled'
  | 'stethoscope.circle'
  | 'stethoscope.circle.fill'
  | 'suitcase'
  | 'suitcase.cart'
  | 'suitcase.cart.fill'
  | 'suitcase.fill'
  | 'sun.and.horizon'
  | 'sun.and.horizon.fill'
  | 'sun.max.circle'
  | 'sun.max.circle.fill'
  | 'tablecells.fill.badge.ellipsis'
  | 'tag.square'
  | 'tag.square.fill'
  | 'takeoutbag.and.cup.and.straw'
  | 'takeoutbag.and.cup.and.straw.fill'
  | 'teletype.answer.circle'
  | 'teletype.answer.circle.fill'
  | 'testtube.2'
  | 'text.viewfinder'
  | 'textformat.123.hi'
  | 'theatermasks'
  | 'theatermasks.circle'
  | 'theatermasks.circle.fill'
  | 'theatermasks.fill'
  | 'train.side.front.car'
  | 'train.side.middle.car'
  | 'train.side.rear.car'
  | 'tram.fill.tunnel'
  | 'trapezoid.and.line.horizontal'
  | 'trapezoid.and.line.horizontal.fill'
  | 'trapezoid.and.line.vertical'
  | 'trapezoid.and.line.vertical.fill'
  | 'trash.slash.circle'
  | 'trash.slash.circle.fill'
  | 'trash.slash.square'
  | 'trash.slash.square.fill'
  | 'trash.square'
  | 'trash.square.fill'
  | 'triangle.bottomhalf.filled'
  | 'triangle.inset.filled'
  | 'triangle.lefthalf.filled'
  | 'triangle.righthalf.filled'
  | 'triangle.tophalf.filled'
  | 'tshirt'
  | 'tshirt.fill'
  | 'tv.inset.filled'
  | 'video.and.waveform'
  | 'video.and.waveform.fill'
  | 'video.badge.ellipsis'
  | 'video.fill.badge.ellipsis'
  | 'video.square'
  | 'video.square.fill'
  | 'wake.circle'
  | 'wake.circle.fill'
  | 'watchface.applewatch.case'
  | 'waveform.and.magnifyingglass'
  | 'waveform.and.mic'
  | 'waveform.badge.exclamationmark'
  | 'waveform.badge.minus'
  | 'waveform.badge.plus'
  | 'wifi.circle'
  | 'wifi.circle.fill'
  | 'wifi.square'
  | 'wifi.square.fill'
  | 'xmark.app'
  | 'xmark.app.fill'

/**
 * @name SF Symbols 3.1
 * @description These symbols are available on the following platforms:
 * iOS v15.1+,
 * macOS v12.0+,
 * tvOS v15.1+,
 * visionOS v1.0+,
 * watchOS v8.1+
 */
export type SFSymbols3_1 =
  | SFSymbols3_0
  | 'bolt.ring.closed'
  | 'platter.filled.bottom.and.arrow.down.iphone'
  | 'platter.filled.bottom.iphone'
  | 'platter.filled.top.and.arrow.up.iphone'
  | 'platter.filled.top.iphone'
  | 'square.3.layers.3d.down.backward'
  | 'square.3.layers.3d.down.forward'
  | 'square.3.layers.3d.down.left'
  | 'square.3.layers.3d.down.right'
  | 'text.justify.leading'
  | 'text.justify.left'
  | 'text.justify.right'
  | 'text.justify.trailing'

/**
 * @name SF Symbols 3.2
 * @description These symbols are available on the following platforms:
 * iOS v15.2+,
 * macOS v12.1+,
 * tvOS v15.2+,
 * visionOS v1.0+,
 * watchOS v8.3+
 */
export type SFSymbols3_2 =
  | SFSymbols3_1
  | 'airpod.gen3.left'
  | 'airpod.gen3.right'
  | 'airpods.gen3'
  | 'airpods.gen3.chargingcase.wireless'
  | 'airpods.gen3.chargingcase.wireless.fill'
  | 'beats.fit.pro'
  | 'beats.fit.pro.chargingcase'
  | 'beats.fit.pro.chargingcase.fill'
  | 'beats.fit.pro.left'
  | 'beats.fit.pro.right'
  | 'rectangle.leadinghalf.filled'
  | 'rectangle.trailinghalf.filled'
  | 'square.3.layers.3d.down.left.slash'
  | 'square.3.layers.3d.down.right.slash'
  | 'square.3.stack.3d.slash'

/**
 * @name SF Symbols 3.3
 * @description These symbols are available on the following platforms:
 * iOS v15.4+,
 * macOS v12.3+,
 * tvOS v15.4+,
 * visionOS v1.0+,
 * watchOS v8.5+
 */
export type SFSymbols3_3 =
  | SFSymbols3_2
  | 'camera.macro'
  | 'camera.macro.circle'
  | 'camera.macro.circle.fill'
  | 'dots.and.line.vertical.and.cursorarrow.rectangle'
  | 'key.viewfinder'
  | 'person.badge.key'
  | 'person.badge.key.fill'

/**
 * @name SF Symbols 4.0
 * @description These symbols are available on the following platforms:
 * iOS v16.0+,
 * macOS v13.0+,
 * tvOS v16.0+,
 * visionOS v1.0+,
 * watchOS v9.0+
 */
export type SFSymbols4_0 =
  | SFSymbols3_3
  | 'abs.brakesignal'
  | 'air.conditioner.horizontal'
  | 'air.conditioner.horizontal.fill'
  | 'air.conditioner.vertical'
  | 'air.conditioner.vertical.fill'
  | 'air.purifier'
  | 'air.purifier.fill'
  | 'alarm.waves.left.and.right'
  | 'alarm.waves.left.and.right.fill'
  | 'allergens.fill'
  | 'angle'
  | 'apple.logo'
  | 'arrow.down.and.line.horizontal.and.arrow.up'
  | 'arrow.down.message'
  | 'arrow.down.message.fill'
  | 'arrow.left.and.line.vertical.and.arrow.right'
  | 'arrow.left.and.right.text.vertical'
  | 'arrow.right.and.line.vertical.and.arrow.left'
  | 'arrow.up.and.down.and.sparkles'
  | 'arrow.up.and.down.text.horizontal'
  | 'arrow.up.and.line.horizontal.and.arrow.down'
  | 'arrow.up.circle.badge.clock'
  | 'arrowshape.backward'
  | 'arrowshape.backward.fill'
  | 'arrowshape.forward'
  | 'arrowshape.forward.fill'
  | 'arrowshape.left'
  | 'arrowshape.left.fill'
  | 'arrowshape.right'
  | 'arrowshape.right.fill'
  | 'arrowshape.turn.up.backward.badge.clock'
  | 'arrowshape.turn.up.backward.badge.clock.fill'
  | 'arrowshape.turn.up.backward.badge.clock.fill.rtl'
  | 'arrowshape.turn.up.backward.badge.clock.rtl'
  | 'australsign'
  | 'av.remote'
  | 'av.remote.fill'
  | 'backpack'
  | 'backpack.fill'
  | 'backward.end.circle'
  | 'backward.end.circle.fill'
  | 'bag.badge.questionmark'
  | 'bag.badge.questionmark.ar'
  | 'bag.fill.badge.questionmark'
  | 'bag.fill.badge.questionmark.ar'
  | 'bahtsign'
  | 'balloon'
  | 'balloon.2'
  | 'balloon.2.fill'
  | 'balloon.fill'
  | 'baseball'
  | 'baseball.circle'
  | 'baseball.circle.fill'
  | 'baseball.diamond.bases'
  | 'baseball.fill'
  | 'basket'
  | 'basket.fill'
  | 'basketball'
  | 'basketball.circle'
  | 'basketball.circle.fill'
  | 'basketball.fill'
  | 'bathtub'
  | 'bathtub.fill'
  | 'battery.100.circle'
  | 'battery.100.circle.fill'
  | 'beach.umbrella'
  | 'beach.umbrella.fill'
  | 'bell.and.waves.left.and.right'
  | 'bell.and.waves.left.and.right.fill'
  | 'bird'
  | 'bird.fill'
  | 'birthday.cake'
  | 'birthday.cake.fill'
  | 'bitcoinsign'
  | 'blinds.horizontal.closed'
  | 'blinds.horizontal.open'
  | 'blinds.vertical.closed'
  | 'blinds.vertical.open'
  | 'bolt.badge.clock'
  | 'bolt.badge.clock.fill'
  | 'bolt.brakesignal'
  | 'box.truck'
  | 'box.truck.badge.clock'
  | 'box.truck.badge.clock.fill'
  | 'box.truck.badge.clock.fill.rtl'
  | 'box.truck.badge.clock.rtl'
  | 'box.truck.fill'
  | 'brakesignal'
  | 'brakesignal.dashed'
  | 'brazilianrealsign'
  | 'bubbles.and.sparkles'
  | 'bubbles.and.sparkles.fill'
  | 'button.programmable'
  | 'button.programmable.square'
  | 'button.programmable.square.fill'
  | 'cabinet'
  | 'cabinet.fill'
  | 'carbon.dioxide.cloud'
  | 'carbon.dioxide.cloud.fill'
  | 'carbon.monoxide.cloud'
  | 'carbon.monoxide.cloud.fill'
  | 'carrot'
  | 'carrot.fill'
  | 'cart.badge.questionmark'
  | 'cart.badge.questionmark.ar'
  | 'cart.badge.questionmark.rtl'
  | 'cart.fill.badge.questionmark'
  | 'cart.fill.badge.questionmark.ar'
  | 'cart.fill.badge.questionmark.rtl'
  | 'cedisign'
  | 'cellularbars'
  | 'centsign'
  | 'chair'
  | 'chair.fill'
  | 'chair.lounge'
  | 'chair.lounge.fill'
  | 'chandelier'
  | 'chandelier.fill'
  | 'character.duployan'
  | 'character.phonetic'
  | 'character.sutton'
  | 'chart.line.downtrend.xyaxis'
  | 'chart.line.downtrend.xyaxis.circle'
  | 'chart.line.downtrend.xyaxis.circle.fill'
  | 'chart.line.flattrend.xyaxis'
  | 'chart.line.flattrend.xyaxis.circle'
  | 'chart.line.flattrend.xyaxis.circle.fill'
  | 'checklist.checked'
  | 'checklist.checked.rtl'
  | 'checklist.unchecked'
  | 'checkmark.circle.badge.questionmark'
  | 'checkmark.circle.badge.questionmark.ar'
  | 'checkmark.circle.badge.questionmark.fill'
  | 'checkmark.circle.badge.questionmark.fill.ar'
  | 'checkmark.circle.badge.xmark'
  | 'checkmark.circle.badge.xmark.fill'
  | 'checkmark.message'
  | 'checkmark.message.fill'
  | 'chevron.backward.to.line'
  | 'chevron.forward.to.line'
  | 'chevron.left.to.line'
  | 'chevron.right.to.line'
  | 'circle.dashed.rectangle'
  | 'circle.filled.pattern.diagonalline.rectangle'
  | 'circle.rectangle.dashed'
  | 'circle.rectangle.filled.pattern.diagonalline'
  | 'clipboard'
  | 'clipboard.fill'
  | 'clock.badge'
  | 'clock.badge.fill'
  | 'clock.badge.questionmark'
  | 'clock.badge.questionmark.ar'
  | 'clock.badge.questionmark.fill'
  | 'clock.badge.questionmark.fill.ar'
  | 'clock.badge.xmark'
  | 'clock.badge.xmark.fill'
  | 'cloud.bolt.circle'
  | 'cloud.bolt.circle.fill'
  | 'cloud.bolt.rain.circle'
  | 'cloud.bolt.rain.circle.fill'
  | 'cloud.circle'
  | 'cloud.circle.fill'
  | 'cloud.drizzle.circle'
  | 'cloud.drizzle.circle.fill'
  | 'cloud.fog.circle'
  | 'cloud.fog.circle.fill'
  | 'cloud.hail.circle'
  | 'cloud.hail.circle.fill'
  | 'cloud.heavyrain.circle'
  | 'cloud.heavyrain.circle.fill'
  | 'cloud.moon.bolt.circle'
  | 'cloud.moon.bolt.circle.fill'
  | 'cloud.moon.circle'
  | 'cloud.moon.circle.fill'
  | 'cloud.moon.rain.circle'
  | 'cloud.moon.rain.circle.fill'
  | 'cloud.rain.circle'
  | 'cloud.rain.circle.fill'
  | 'cloud.sleet.circle'
  | 'cloud.sleet.circle.fill'
  | 'cloud.snow.circle'
  | 'cloud.snow.circle.fill'
  | 'cloud.sun.bolt.circle'
  | 'cloud.sun.bolt.circle.fill'
  | 'cloud.sun.circle'
  | 'cloud.sun.circle.fill'
  | 'cloud.sun.rain.circle'
  | 'cloud.sun.rain.circle.fill'
  | 'coloncurrencysign'
  | 'compass.drawing'
  | 'contact.sensor'
  | 'contact.sensor.fill'
  | 'cooktop'
  | 'cooktop.fill'
  | 'creditcard.viewfinder'
  | 'cricket.ball'
  | 'cricket.ball.circle'
  | 'cricket.ball.circle.fill'
  | 'cricket.ball.fill'
  | 'cruzeirosign'
  | 'cursorarrow.square.fill'
  | 'curtains.closed'
  | 'curtains.open'
  | 'dehumidifier'
  | 'dehumidifier.fill'
  | 'deskview'
  | 'deskview.fill'
  | 'dial.high'
  | 'dial.high.fill'
  | 'dial.low'
  | 'dial.low.fill'
  | 'dial.medium'
  | 'dial.medium.fill'
  | 'dishwasher'
  | 'dishwasher.fill'
  | 'distribute.horizontal.center'
  | 'distribute.horizontal.center.fill'
  | 'distribute.horizontal.left'
  | 'distribute.horizontal.left.fill'
  | 'distribute.horizontal.right'
  | 'distribute.horizontal.right.fill'
  | 'distribute.vertical.bottom'
  | 'distribute.vertical.bottom.fill'
  | 'distribute.vertical.center'
  | 'distribute.vertical.center.fill'
  | 'distribute.vertical.top'
  | 'distribute.vertical.top.fill'
  | 'doc.badge.arrow.up'
  | 'doc.badge.arrow.up.fill'
  | 'dollarsign'
  | 'dollarsign.arrow.circlepath'
  | 'dongsign'
  | 'door.french.closed'
  | 'door.french.open'
  | 'door.garage.closed'
  | 'door.garage.closed.trianglebadge.exclamationmark'
  | 'door.garage.double.bay.closed'
  | 'door.garage.double.bay.closed.trianglebadge.exclamationmark'
  | 'door.garage.double.bay.open'
  | 'door.garage.double.bay.open.trianglebadge.exclamationmark'
  | 'door.garage.open'
  | 'door.garage.open.trianglebadge.exclamationmark'
  | 'door.left.hand.closed'
  | 'door.left.hand.open'
  | 'door.right.hand.closed'
  | 'door.right.hand.open'
  | 'door.sliding.left.hand.closed'
  | 'door.sliding.left.hand.open'
  | 'door.sliding.right.hand.closed'
  | 'door.sliding.right.hand.open'
  | 'drop.degreesign'
  | 'drop.degreesign.fill'
  | 'drop.degreesign.slash'
  | 'drop.degreesign.slash.fill'
  | 'drop.degreesign.slash.fill.rtl'
  | 'drop.degreesign.slash.rtl'
  | 'drop.keypad.rectangle'
  | 'drop.keypad.rectangle.fill'
  | 'dryer'
  | 'dryer.fill'
  | 'dumbbell'
  | 'dumbbell.fill'
  | 'ellipsis.message'
  | 'ellipsis.message.fill'
  | 'entry.lever.keypad'
  | 'entry.lever.keypad.fill'
  | 'entry.lever.keypad.trianglebadge.exclamationmark'
  | 'entry.lever.keypad.trianglebadge.exclamationmark.fill'
  | 'envelope.open.badge.clock'
  | 'eraser'
  | 'eraser.fill'
  | 'eraser.line.dashed'
  | 'eraser.line.dashed.fill'
  | 'eurosign'
  | 'exclamationmark.brakesignal'
  | 'exclamationmark.lock'
  | 'exclamationmark.lock.fill'
  | 'exclamationmark.questionmark'
  | 'exclamationmark.questionmark.ar'
  | 'externaldrive.badge.exclamationmark'
  | 'externaldrive.badge.questionmark'
  | 'externaldrive.badge.questionmark.ar'
  | 'externaldrive.fill.badge.exclamationmark'
  | 'externaldrive.fill.badge.questionmark'
  | 'externaldrive.fill.badge.questionmark.ar'
  | 'externaldrive.fill.trianglebadge.exclamationmark'
  | 'externaldrive.trianglebadge.exclamationmark'
  | 'face.smiling.inverse'
  | 'fan.and.light.ceiling'
  | 'fan.and.light.ceiling.fill'
  | 'fan.ceiling'
  | 'fan.ceiling.fill'
  | 'fan.desk'
  | 'fan.desk.fill'
  | 'fan.floor'
  | 'fan.floor.fill'
  | 'fan.oscillation'
  | 'fan.oscillation.fill'
  | 'fanblades.slash'
  | 'fanblades.slash.fill'
  | 'faxmachine.fill'
  | 'figure.2.and.child.holdinghands'
  | 'figure.2.arms.open'
  | 'figure.american.football'
  | 'figure.and.child.holdinghands'
  | 'figure.archery'
  | 'figure.arms.open'
  | 'figure.australian.football'
  | 'figure.badminton'
  | 'figure.barre'
  | 'figure.baseball'
  | 'figure.basketball'
  | 'figure.bowling'
  | 'figure.boxing'
  | 'figure.climbing'
  | 'figure.cooldown'
  | 'figure.core.training'
  | 'figure.cricket'
  | 'figure.cross.training'
  | 'figure.curling'
  | 'figure.dance'
  | 'figure.disc.sports'
  | 'figure.dress.line.vertical.figure'
  | 'figure.elliptical'
  | 'figure.equestrian.sports'
  | 'figure.fall'
  | 'figure.fall.circle'
  | 'figure.fall.circle.fill'
  | 'figure.fencing'
  | 'figure.fishing'
  | 'figure.flexibility'
  | 'figure.golf'
  | 'figure.gymnastics'
  | 'figure.hand.cycling'
  | 'figure.handball'
  | 'figure.highintensity.intervaltraining'
  | 'figure.hiking'
  | 'figure.hockey'
  | 'figure.hunting'
  | 'figure.indoor.cycle'
  | 'figure.jumprope'
  | 'figure.kickboxing'
  | 'figure.lacrosse'
  | 'figure.martial.arts'
  | 'figure.mind.and.body'
  | 'figure.mixed.cardio'
  | 'figure.open.water.swim'
  | 'figure.outdoor.cycle'
  | 'figure.pickleball'
  | 'figure.pilates'
  | 'figure.play'
  | 'figure.pool.swim'
  | 'figure.racquetball'
  | 'figure.roll.runningpace'
  | 'figure.rolling'
  | 'figure.rower'
  | 'figure.rugby'
  | 'figure.run'
  | 'figure.run.circle'
  | 'figure.run.circle.fill'
  | 'figure.sailing'
  | 'figure.skating'
  | 'figure.skiing.crosscountry'
  | 'figure.skiing.downhill'
  | 'figure.snowboarding'
  | 'figure.soccer'
  | 'figure.socialdance'
  | 'figure.softball'
  | 'figure.squash'
  | 'figure.stair.stepper'
  | 'figure.stairs'
  | 'figure.step.training'
  | 'figure.strengthtraining.functional'
  | 'figure.strengthtraining.traditional'
  | 'figure.surfing'
  | 'figure.table.tennis'
  | 'figure.taichi'
  | 'figure.tennis'
  | 'figure.track.and.field'
  | 'figure.volleyball'
  | 'figure.walk.arrival'
  | 'figure.walk.departure'
  | 'figure.walk.motion'
  | 'figure.water.fitness'
  | 'figure.waterpolo'
  | 'figure.wrestling'
  | 'figure.yoga'
  | 'film.stack'
  | 'film.stack.fill'
  | 'fireplace'
  | 'fireplace.fill'
  | 'firewall'
  | 'firewall.fill'
  | 'fish'
  | 'fish.fill'
  | 'flag.2.crossed.circle'
  | 'flag.2.crossed.circle.fill'
  | 'flag.checkered'
  | 'flag.checkered.2.crossed'
  | 'fleuron'
  | 'fleuron.fill'
  | 'florinsign'
  | 'fluid.brakesignal'
  | 'football'
  | 'football.circle'
  | 'football.circle.fill'
  | 'football.fill'
  | 'forward.end.circle'
  | 'forward.end.circle.fill'
  | 'fossil.shell'
  | 'fossil.shell.fill'
  | 'francsign'
  | 'frying.pan'
  | 'frying.pan.fill'
  | 'gauge.high'
  | 'gauge.low'
  | 'gauge.medium'
  | 'gauge.medium.badge.minus'
  | 'gauge.medium.badge.plus'
  | 'gear.badge'
  | 'gear.badge.rtl'
  | 'gearshape.arrow.triangle.2.circlepath'
  | 'globe.central.south.asia'
  | 'globe.central.south.asia.fill'
  | 'globe.desk'
  | 'globe.desk.fill'
  | 'guaranisign'
  | 'hand.app'
  | 'hand.app.fill'
  | 'hand.raised.fingers.spread'
  | 'hand.raised.fingers.spread.fill'
  | 'headlight.high.beam'
  | 'headlight.high.beam.fill'
  | 'headlight.low.beam'
  | 'headlight.low.beam.fill'
  | 'hearingdevice.and.signal.meter'
  | 'hearingdevice.and.signal.meter.fill'
  | 'hearingdevice.ear.fill'
  | 'heater.vertical'
  | 'heater.vertical.fill'
  | 'hifireceiver'
  | 'hifireceiver.fill'
  | 'hifispeaker.and.appletv.fill.rtl'
  | 'hifispeaker.and.appletv.rtl'
  | 'hockey.puck'
  | 'hockey.puck.circle'
  | 'hockey.puck.circle.fill'
  | 'hockey.puck.fill'
  | 'hold.brakesignal'
  | 'homepod.and.appletv.fill.rtl'
  | 'homepod.and.appletv.rtl'
  | 'homepodmini.and.appletv.fill.rtl'
  | 'homepodmini.and.appletv.rtl'
  | 'hryvniasign'
  | 'humidifier'
  | 'humidifier.and.droplets'
  | 'humidifier.and.droplets.fill'
  | 'humidifier.fill'
  | 'hurricane.circle'
  | 'hurricane.circle.fill'
  | 'indianrupeesign'
  | 'info.bubble'
  | 'info.bubble.fill'
  | 'info.square'
  | 'info.square.fill'
  | 'keyboard.badge.ellipsis.fill'
  | 'keyboard.badge.eye'
  | 'keyboard.badge.eye.fill'
  | 'keyboard.chevron.compact.down.fill'
  | 'keyboard.chevron.compact.left.fill'
  | 'keyboard.onehanded.left.fill'
  | 'keyboard.onehanded.right.fill'
  | 'kipsign'
  | 'lamp.ceiling'
  | 'lamp.ceiling.fill'
  | 'lamp.ceiling.inverse'
  | 'lamp.desk'
  | 'lamp.desk.fill'
  | 'lamp.floor'
  | 'lamp.floor.fill'
  | 'lamp.table'
  | 'lamp.table.fill'
  | 'laptopcomputer.and.ipad'
  | 'larisign'
  | 'laurel.leading'
  | 'laurel.trailing'
  | 'light.beacon.max'
  | 'light.beacon.max.fill'
  | 'light.beacon.min'
  | 'light.beacon.min.fill'
  | 'light.cylindrical.ceiling'
  | 'light.cylindrical.ceiling.fill'
  | 'light.cylindrical.ceiling.inverse'
  | 'light.panel'
  | 'light.panel.fill'
  | 'light.recessed'
  | 'light.recessed.3'
  | 'light.recessed.3.fill'
  | 'light.recessed.3.inverse'
  | 'light.recessed.fill'
  | 'light.recessed.inverse'
  | 'light.ribbon'
  | 'light.ribbon.fill'
  | 'light.strip.2'
  | 'light.strip.2.fill'
  | 'lightbulb.2'
  | 'lightbulb.2.fill'
  | 'lightbulb.led'
  | 'lightbulb.led.fill'
  | 'lightbulb.led.wide'
  | 'lightbulb.led.wide.fill'
  | 'lightswitch.off'
  | 'lightswitch.off.fill'
  | 'lightswitch.off.square'
  | 'lightswitch.off.square.fill'
  | 'lightswitch.on'
  | 'lightswitch.on.fill'
  | 'lightswitch.on.square'
  | 'lightswitch.on.square.fill'
  | 'lirasign'
  | 'list.bullet.clipboard'
  | 'list.bullet.clipboard.fill'
  | 'list.clipboard'
  | 'list.clipboard.fill'
  | 'lizard'
  | 'lizard.fill'
  | 'location.slash.circle'
  | 'location.slash.circle.fill'
  | 'lock.open.trianglebadge.exclamationmark'
  | 'lock.open.trianglebadge.exclamationmark.fill'
  | 'lock.trianglebadge.exclamationmark'
  | 'lock.trianglebadge.exclamationmark.fill'
  | 'macstudio'
  | 'macstudio.fill'
  | 'manatsign'
  | 'medal'
  | 'medal.fill'
  | 'medical.thermometer'
  | 'medical.thermometer.fill'
  | 'message.badge'
  | 'message.badge.circle'
  | 'message.badge.circle.fill'
  | 'message.badge.circle.fill.rtl'
  | 'message.badge.circle.rtl'
  | 'message.badge.fill'
  | 'message.badge.fill.rtl'
  | 'message.badge.filled.fill'
  | 'message.badge.filled.fill.rtl'
  | 'message.badge.rtl'
  | 'mic.and.signal.meter'
  | 'mic.and.signal.meter.fill'
  | 'mic.badge.xmark'
  | 'mic.fill.badge.xmark'
  | 'microbe'
  | 'microbe.circle'
  | 'microbe.circle.fill'
  | 'microbe.fill'
  | 'microwave'
  | 'microwave.fill'
  | 'millsign'
  | 'mirror.side.left'
  | 'mirror.side.right'
  | 'moon.haze'
  | 'moon.haze.circle'
  | 'moon.haze.circle.fill'
  | 'moon.haze.fill'
  | 'moon.stars.circle'
  | 'moon.stars.circle.fill'
  | 'moonphase.first.quarter'
  | 'moonphase.first.quarter.inverse'
  | 'moonphase.full.moon'
  | 'moonphase.full.moon.inverse'
  | 'moonphase.last.quarter'
  | 'moonphase.last.quarter.inverse'
  | 'moonphase.new.moon'
  | 'moonphase.new.moon.inverse'
  | 'moonphase.waning.crescent'
  | 'moonphase.waning.crescent.inverse'
  | 'moonphase.waning.gibbous'
  | 'moonphase.waning.gibbous.inverse'
  | 'moonphase.waxing.crescent'
  | 'moonphase.waxing.crescent.inverse'
  | 'moonphase.waxing.gibbous'
  | 'moonphase.waxing.gibbous.inverse'
  | 'nairasign'
  | 'nosign.app'
  | 'nosign.app.fill'
  | 'numbersign'
  | 'oar.2.crossed'
  | 'opticaldisc.fill'
  | 'oven'
  | 'oven.fill'
  | 'parkinglight'
  | 'parkinglight.fill'
  | 'parkingsign.brakesignal'
  | 'parkingsign.brakesignal.slash'
  | 'party.popper'
  | 'party.popper.fill'
  | 'pedestrian.gate.closed'
  | 'pedestrian.gate.open'
  | 'pencil.and.ruler'
  | 'pencil.and.ruler.fill'
  | 'pencil.line'
  | 'person.2.badge.gearshape'
  | 'person.2.badge.gearshape.fill'
  | 'person.2.gobackward'
  | 'person.2.slash'
  | 'person.2.slash.fill'
  | 'person.and.background.dotted'
  | 'person.badge.shield.checkmark'
  | 'person.badge.shield.checkmark.fill'
  | 'person.bust'
  | 'person.bust.fill'
  | 'person.crop.rectangle.badge.plus'
  | 'person.crop.rectangle.badge.plus.fill'
  | 'person.line.dotted.person'
  | 'person.line.dotted.person.fill'
  | 'pesetasign'
  | 'pesosign'
  | 'phone.arrow.down.left.fill'
  | 'phone.arrow.right.fill'
  | 'phone.arrow.up.right.circle'
  | 'phone.arrow.up.right.circle.fill'
  | 'phone.arrow.up.right.fill'
  | 'phone.badge.checkmark'
  | 'phone.connection.fill'
  | 'phone.down.waves.left.and.right'
  | 'phone.fill.badge.checkmark'
  | 'photo.stack'
  | 'photo.stack.fill'
  | 'pill'
  | 'pill.circle'
  | 'pill.circle.fill'
  | 'pill.fill'
  | 'pipe.and.drop'
  | 'pipe.and.drop.fill'
  | 'play.desktopcomputer'
  | 'play.display'
  | 'play.laptopcomputer'
  | 'playpause.circle'
  | 'playpause.circle.fill'
  | 'playstation.logo'
  | 'popcorn'
  | 'popcorn.circle'
  | 'popcorn.circle.fill'
  | 'popcorn.fill'
  | 'poweroutlet.strip'
  | 'poweroutlet.strip.fill'
  | 'poweroutlet.type.a'
  | 'poweroutlet.type.a.fill'
  | 'poweroutlet.type.a.square'
  | 'poweroutlet.type.a.square.fill'
  | 'poweroutlet.type.b'
  | 'poweroutlet.type.b.fill'
  | 'poweroutlet.type.b.square'
  | 'poweroutlet.type.b.square.fill'
  | 'poweroutlet.type.c'
  | 'poweroutlet.type.c.fill'
  | 'poweroutlet.type.c.square'
  | 'poweroutlet.type.c.square.fill'
  | 'poweroutlet.type.d'
  | 'poweroutlet.type.d.fill'
  | 'poweroutlet.type.d.square'
  | 'poweroutlet.type.d.square.fill'
  | 'poweroutlet.type.e'
  | 'poweroutlet.type.e.fill'
  | 'poweroutlet.type.e.square'
  | 'poweroutlet.type.e.square.fill'
  | 'poweroutlet.type.f'
  | 'poweroutlet.type.f.fill'
  | 'poweroutlet.type.f.square'
  | 'poweroutlet.type.f.square.fill'
  | 'poweroutlet.type.g'
  | 'poweroutlet.type.g.fill'
  | 'poweroutlet.type.g.square'
  | 'poweroutlet.type.g.square.fill'
  | 'poweroutlet.type.h'
  | 'poweroutlet.type.h.fill'
  | 'poweroutlet.type.h.square'
  | 'poweroutlet.type.h.square.fill'
  | 'poweroutlet.type.i'
  | 'poweroutlet.type.i.fill'
  | 'poweroutlet.type.i.square'
  | 'poweroutlet.type.i.square.fill'
  | 'poweroutlet.type.j'
  | 'poweroutlet.type.j.fill'
  | 'poweroutlet.type.j.square'
  | 'poweroutlet.type.j.square.fill'
  | 'poweroutlet.type.k'
  | 'poweroutlet.type.k.fill'
  | 'poweroutlet.type.k.square'
  | 'poweroutlet.type.k.square.fill'
  | 'poweroutlet.type.l'
  | 'poweroutlet.type.l.fill'
  | 'poweroutlet.type.l.square'
  | 'poweroutlet.type.l.square.fill'
  | 'poweroutlet.type.m'
  | 'poweroutlet.type.m.fill'
  | 'poweroutlet.type.m.square'
  | 'poweroutlet.type.m.square.fill'
  | 'poweroutlet.type.n'
  | 'poweroutlet.type.n.fill'
  | 'poweroutlet.type.n.square'
  | 'poweroutlet.type.n.square.fill'
  | 'poweroutlet.type.o'
  | 'poweroutlet.type.o.fill'
  | 'poweroutlet.type.o.square'
  | 'poweroutlet.type.o.square.fill'
  | 'questionmark.bubble'
  | 'questionmark.bubble.ar'
  | 'questionmark.bubble.fill'
  | 'questionmark.bubble.fill.ar'
  | 'quotelevel'
  | 'recordingtape.circle'
  | 'recordingtape.circle.fill'
  | 'rectangle.portrait.and.arrow.forward'
  | 'rectangle.portrait.and.arrow.forward.fill'
  | 'rectangle.portrait.on.rectangle.portrait.angled'
  | 'rectangle.portrait.on.rectangle.portrait.angled.fill'
  | 'refrigerator'
  | 'refrigerator.fill'
  | 'road.lanes'
  | 'road.lanes.curved.left'
  | 'road.lanes.curved.right'
  | 'roller.shade.closed'
  | 'roller.shade.open'
  | 'roman.shade.closed'
  | 'roman.shade.open'
  | 'rublesign'
  | 'rupeesign'
  | 'sailboat'
  | 'sailboat.fill'
  | 'sensor'
  | 'sensor.fill'
  | 'shared.with.you'
  | 'shared.with.you.slash'
  | 'shazam.logo'
  | 'shazam.logo.fill'
  | 'shekelsign'
  | 'shippingbox.and.arrow.backward'
  | 'shippingbox.and.arrow.backward.fill'
  | 'shoeprints.fill'
  | 'shower'
  | 'shower.fill'
  | 'shower.handheld'
  | 'shower.handheld.fill'
  | 'shower.sidejet'
  | 'shower.sidejet.fill'
  | 'sink'
  | 'sink.fill'
  | 'slider.horizontal.2.gobackward'
  | 'slider.horizontal.2.square.badge.arrow.down'
  | 'slider.horizontal.2.square.on.square'
  | 'slider.horizontal.below.square.and.square.filled'
  | 'smoke.circle'
  | 'smoke.circle.fill'
  | 'soccerball'
  | 'soccerball.circle'
  | 'soccerball.circle.fill'
  | 'soccerball.circle.fill.inverse'
  | 'soccerball.circle.inverse'
  | 'soccerball.inverse'
  | 'sofa'
  | 'sofa.fill'
  | 'space'
  | 'speaker.minus'
  | 'speaker.minus.fill'
  | 'speaker.plus'
  | 'speaker.plus.fill'
  | 'speaker.square'
  | 'speaker.square.fill'
  | 'speaker.wave.2.bubble.left'
  | 'speaker.wave.2.bubble.left.fill'
  | 'speaker.wave.2.bubble.left.fill.rtl'
  | 'speaker.wave.2.bubble.left.rtl'
  | 'spigot'
  | 'spigot.fill'
  | 'sportscourt.circle'
  | 'sportscourt.circle.fill'
  | 'sprinkler'
  | 'sprinkler.and.droplets'
  | 'sprinkler.and.droplets.fill'
  | 'sprinkler.fill'
  | 'square.2.layers.3d'
  | 'square.2.layers.3d.bottom.filled'
  | 'square.2.layers.3d.top.filled'
  | 'square.3.layers.3d'
  | 'square.3.layers.3d.bottom.filled'
  | 'square.3.layers.3d.down.backward.slash.rtl'
  | 'square.3.layers.3d.down.forward.slash.rtl'
  | 'square.3.layers.3d.middle.filled'
  | 'square.3.layers.3d.slash'
  | 'square.3.layers.3d.top.filled'
  | 'square.and.pencil.circle'
  | 'square.and.pencil.circle.fill'
  | 'square.bottomthird.inset.filled'
  | 'square.dotted'
  | 'square.leadingthird.inset.filled'
  | 'square.leftthird.inset.filled'
  | 'square.on.square.badge.person.crop'
  | 'square.on.square.badge.person.crop.fill'
  | 'square.on.square.intersection.dashed'
  | 'square.rightthird.inset.filled'
  | 'square.topthird.inset.filled'
  | 'square.trailingthird.inset.filled'
  | 'squares.leading.rectangle'
  | 'squareshape.dotted.split.2x2'
  | 'stairs'
  | 'star.square.on.square'
  | 'star.square.on.square.fill'
  | 'sterlingsign'
  | 'stove'
  | 'stove.fill'
  | 'sun.and.horizon.circle'
  | 'sun.and.horizon.circle.fill'
  | 'sun.dust.circle'
  | 'sun.dust.circle.fill'
  | 'sun.haze.circle'
  | 'sun.haze.circle.fill'
  | 'sun.max.trianglebadge.exclamationmark'
  | 'sun.max.trianglebadge.exclamationmark.fill'
  | 'sunrise.circle'
  | 'sunrise.circle.fill'
  | 'sunset.circle'
  | 'sunset.circle.fill'
  | 'swatchpalette'
  | 'swatchpalette.fill'
  | 'switch.programmable'
  | 'switch.programmable.fill'
  | 'switch.programmable.square'
  | 'switch.programmable.square.fill'
  | 'syringe'
  | 'syringe.fill'
  | 'table.furniture'
  | 'table.furniture.fill'
  | 'teddybear'
  | 'teddybear.fill'
  | 'tengesign'
  | 'tennis.racket'
  | 'tennis.racket.circle'
  | 'tennis.racket.circle.fill'
  | 'tennisball'
  | 'tennisball.circle'
  | 'tennisball.circle.fill'
  | 'tennisball.fill'
  | 'tent'
  | 'tent.fill'
  | 'text.line.first.and.arrowtriangle.forward'
  | 'text.line.last.and.arrowtriangle.forward'
  | 'text.word.spacing'
  | 'textformat.12'
  | 'textformat.12.ar'
  | 'textformat.12.hi'
  | 'theatermask.and.paintbrush'
  | 'theatermask.and.paintbrush.fill'
  | 'thermometer.brakesignal'
  | 'thermometer.high'
  | 'thermometer.low'
  | 'thermometer.medium'
  | 'thermometer.medium.slash'
  | 'thermometer.snowflake.circle'
  | 'thermometer.snowflake.circle.fill'
  | 'thermometer.sun.circle'
  | 'thermometer.sun.circle.fill'
  | 'timer.circle'
  | 'timer.circle.fill'
  | 'toilet'
  | 'toilet.fill'
  | 'tornado.circle'
  | 'tornado.circle.fill'
  | 'trophy'
  | 'trophy.circle'
  | 'trophy.circle.fill'
  | 'trophy.fill'
  | 'tropicalstorm.circle'
  | 'tropicalstorm.circle.fill'
  | 'tugriksign'
  | 'turkishlirasign'
  | 'tv.and.mediabox.fill'
  | 'umbrella.percent'
  | 'umbrella.percent.ar'
  | 'umbrella.percent.fill'
  | 'umbrella.percent.fill.ar'
  | 'vial.viewfinder'
  | 'video.doorbell'
  | 'video.doorbell.fill'
  | 'videoprojector'
  | 'videoprojector.fill'
  | 'volleyball'
  | 'volleyball.circle'
  | 'volleyball.circle.fill'
  | 'volleyball.fill'
  | 'washer'
  | 'washer.fill'
  | 'water.waves'
  | 'water.waves.and.arrow.down'
  | 'water.waves.and.arrow.down.trianglebadge.exclamationmark'
  | 'water.waves.and.arrow.up'
  | 'water.waves.slash'
  | 'waveform.slash'
  | 'web.camera'
  | 'web.camera.fill'
  | 'wifi.router'
  | 'wifi.router.fill'
  | 'wind.circle'
  | 'wind.circle.fill'
  | 'wind.snow.circle'
  | 'wind.snow.circle.fill'
  | 'window.awning'
  | 'window.awning.closed'
  | 'window.casement'
  | 'window.casement.closed'
  | 'window.ceiling'
  | 'window.ceiling.closed'
  | 'window.horizontal'
  | 'window.horizontal.closed'
  | 'window.shade.closed'
  | 'window.shade.open'
  | 'window.vertical.closed'
  | 'window.vertical.open'
  | 'windshield.front.and.fluid'
  | 'windshield.front.and.wiper'
  | 'windshield.front.and.wiper.and.drop'
  | 'windshield.rear.and.fluid'
  | 'windshield.rear.and.wiper'
  | 'wineglass'
  | 'wineglass.fill'
  | 'wonsign'
  | 'wrench.adjustable'
  | 'wrench.adjustable.fill'
  | 'xbox.logo'
  | 'yensign'

/**
 * @name SF Symbols 4.1
 * @description These symbols are available on the following platforms:
 * iOS v16.1+,
 * macOS v13.0+,
 * tvOS v16.1+,
 * visionOS v1.0+,
 * watchOS v9.1+
 */
export type SFSymbols4_1 =
  | SFSymbols4_0
  | '0.circle.ar'
  | '0.circle.fill.ar'
  | '0.square.ar'
  | '0.square.fill.ar'
  | '1.brakesignal'
  | '1.circle.ar'
  | '1.circle.fill.ar'
  | '1.lane'
  | '1.square.ar'
  | '1.square.fill.ar'
  | '10.lane'
  | '11.lane'
  | '12.lane'
  | '2.brakesignal'
  | '2.circle.ar'
  | '2.circle.fill.ar'
  | '2.lane'
  | '2.square.ar'
  | '2.square.fill.ar'
  | '3.circle.ar'
  | '3.circle.fill.ar'
  | '3.lane'
  | '3.square.ar'
  | '3.square.fill.ar'
  | '4.circle.ar'
  | '4.circle.fill.ar'
  | '4.lane'
  | '4.square.ar'
  | '4.square.fill.ar'
  | '5.circle.ar'
  | '5.circle.fill.ar'
  | '5.lane'
  | '5.square.ar'
  | '5.square.fill.ar'
  | '6.circle.ar'
  | '6.circle.fill.ar'
  | '6.lane'
  | '6.square.ar'
  | '6.square.fill.ar'
  | '7.circle.ar'
  | '7.circle.fill.ar'
  | '7.lane'
  | '7.square.ar'
  | '7.square.fill.ar'
  | '8.circle.ar'
  | '8.circle.fill.ar'
  | '8.lane'
  | '8.square.ar'
  | '8.square.fill.ar'
  | '9.circle.ar'
  | '9.circle.fill.ar'
  | '9.lane'
  | '9.square.ar'
  | '9.square.fill.ar'
  | 'abs'
  | 'abs.brakesignal.slash'
  | 'abs.circle'
  | 'abs.circle.fill'
  | 'auto.brakesignal'
  | 'auto.headlight.high.beam'
  | 'auto.headlight.high.beam.fill'
  | 'auto.headlight.low.beam'
  | 'auto.headlight.low.beam.fill'
  | 'autostartstop'
  | 'autostartstop.slash'
  | 'autostartstop.trianglebadge.exclamationmark'
  | 'axel.2'
  | 'axel.2.front.and.rear.engaged'
  | 'axel.2.front.engaged'
  | 'axel.2.rear.engaged'
  | 'backpack.circle'
  | 'backpack.circle.fill'
  | 'batteryblock'
  | 'batteryblock.fill'
  | 'batteryblock.slash'
  | 'batteryblock.slash.fill'
  | 'bolt.trianglebadge.exclamationmark'
  | 'bolt.trianglebadge.exclamationmark.fill'
  | 'car.front.waves.up'
  | 'car.front.waves.up.fill'
  | 'car.rear'
  | 'car.rear.and.tire.marks'
  | 'car.rear.and.tire.marks.slash'
  | 'car.rear.fill'
  | 'car.rear.road.lane'
  | 'car.rear.road.lane.dashed'
  | 'car.rear.waves.up'
  | 'car.rear.waves.up.fill'
  | 'car.side'
  | 'car.side.air.circulate'
  | 'car.side.air.circulate.fill'
  | 'car.side.air.fresh'
  | 'car.side.air.fresh.fill'
  | 'car.side.and.exclamationmark'
  | 'car.side.and.exclamationmark.fill'
  | 'car.side.arrowtriangle.down'
  | 'car.side.arrowtriangle.down.fill'
  | 'car.side.arrowtriangle.up'
  | 'car.side.arrowtriangle.up.arrowtriangle.down'
  | 'car.side.arrowtriangle.up.arrowtriangle.down.fill'
  | 'car.side.arrowtriangle.up.fill'
  | 'car.side.fill'
  | 'car.side.front.open'
  | 'car.side.front.open.fill'
  | 'car.side.rear.open'
  | 'car.side.rear.open.fill'
  | 'car.top.door.front.left.and.front.right.and.rear.left.and.rear.right.open'
  | 'car.top.door.front.left.and.front.right.and.rear.left.and.rear.right.open.fill'
  | 'car.top.door.front.left.and.front.right.and.rear.left.open'
  | 'car.top.door.front.left.and.front.right.and.rear.left.open.fill'
  | 'car.top.door.front.left.and.front.right.and.rear.right.open'
  | 'car.top.door.front.left.and.front.right.and.rear.right.open.fill'
  | 'car.top.door.front.left.and.front.right.open'
  | 'car.top.door.front.left.and.front.right.open.fill'
  | 'car.top.door.front.left.and.rear.left.and.rear.right.open'
  | 'car.top.door.front.left.and.rear.left.and.rear.right.open.fill'
  | 'car.top.door.front.left.and.rear.left.open'
  | 'car.top.door.front.left.and.rear.left.open.fill'
  | 'car.top.door.front.left.and.rear.right.open'
  | 'car.top.door.front.left.and.rear.right.open.fill'
  | 'car.top.door.front.left.open'
  | 'car.top.door.front.left.open.fill'
  | 'car.top.door.front.right.and.rear.left.and.rear.right.open'
  | 'car.top.door.front.right.and.rear.left.and.rear.right.open.fill'
  | 'car.top.door.front.right.and.rear.left.open'
  | 'car.top.door.front.right.and.rear.left.open.fill'
  | 'car.top.door.front.right.and.rear.right.open'
  | 'car.top.door.front.right.and.rear.right.open.fill'
  | 'car.top.door.front.right.open'
  | 'car.top.door.front.right.open.fill'
  | 'car.top.door.rear.left.and.rear.right.open'
  | 'car.top.door.rear.left.and.rear.right.open.fill'
  | 'car.top.door.rear.left.open'
  | 'car.top.door.rear.left.open.fill'
  | 'car.top.door.rear.right.open'
  | 'car.top.door.rear.right.open.fill'
  | 'car.top.lane.dashed.arrowtriangle.inward'
  | 'car.top.lane.dashed.arrowtriangle.inward.fill'
  | 'car.top.lane.dashed.badge.steeringwheel'
  | 'car.top.lane.dashed.badge.steeringwheel.fill'
  | 'car.top.lane.dashed.departure.left'
  | 'car.top.lane.dashed.departure.left.fill'
  | 'car.top.lane.dashed.departure.right'
  | 'car.top.lane.dashed.departure.right.fill'
  | 'car.top.radiowaves.front'
  | 'car.top.radiowaves.front.fill'
  | 'car.top.radiowaves.rear'
  | 'car.top.radiowaves.rear.fill'
  | 'car.top.radiowaves.rear.left'
  | 'car.top.radiowaves.rear.left.and.rear.right'
  | 'car.top.radiowaves.rear.left.and.rear.right.fill'
  | 'car.top.radiowaves.rear.left.fill'
  | 'car.top.radiowaves.rear.right'
  | 'car.top.radiowaves.rear.right.fill'
  | 'chart.dots.scatter'
  | 'cross.case.circle'
  | 'cross.case.circle.fill'
  | 'ellipsis.viewfinder'
  | 'engine.combustion'
  | 'engine.combustion.fill'
  | 'exclamationmark.transmission'
  | 'figure.run.square.stack'
  | 'figure.run.square.stack.fill'
  | 'figure.seated.seatbelt'
  | 'figure.seated.seatbelt.and.airbag.off'
  | 'figure.seated.seatbelt.and.airbag.on'
  | 'figure.seated.side.air.lower'
  | 'figure.seated.side.air.upper'
  | 'figure.seated.side.air.upper.and.lower'
  | 'figure.seated.side.air.windshield'
  | 'figure.seated.side.airbag.off'
  | 'figure.seated.side.airbag.off.2'
  | 'figure.seated.side.airbag.on'
  | 'figure.seated.side.airbag.on.2'
  | 'figure.seated.side.windshield.front.and.heat.waves'
  | 'fish.circle'
  | 'fish.circle.fill'
  | 'flag.checkered.circle'
  | 'flag.checkered.circle.fill'
  | 'fluid.transmission'
  | 'glowplug'
  | 'hand.raised.app'
  | 'hand.raised.app.fill'
  | 'hand.raised.brakesignal'
  | 'hand.raised.brakesignal.slash'
  | 'handbag'
  | 'handbag.fill'
  | 'hazardsign'
  | 'hazardsign.fill'
  | 'headlight.daytime'
  | 'headlight.daytime.fill'
  | 'headlight.fog'
  | 'headlight.fog.fill'
  | 'heat.element.windshield'
  | 'house.and.flag'
  | 'house.and.flag.circle'
  | 'house.and.flag.circle.fill'
  | 'house.and.flag.fill'
  | 'house.lodge'
  | 'house.lodge.circle'
  | 'house.lodge.circle.fill'
  | 'house.lodge.fill'
  | 'info.windshield'
  | 'ipad.and.iphone.slash'
  | 'ipad.gen1'
  | 'ipad.gen1.badge.play'
  | 'ipad.gen1.landscape'
  | 'ipad.gen1.landscape.badge.play'
  | 'ipad.gen2'
  | 'ipad.gen2.badge.play'
  | 'ipad.gen2.landscape'
  | 'ipad.gen2.landscape.badge.play'
  | 'iphone.gen1'
  | 'iphone.gen1.badge.play'
  | 'iphone.gen1.circle'
  | 'iphone.gen1.circle.fill'
  | 'iphone.gen1.landscape'
  | 'iphone.gen1.radiowaves.left.and.right'
  | 'iphone.gen1.radiowaves.left.and.right.circle'
  | 'iphone.gen1.radiowaves.left.and.right.circle.fill'
  | 'iphone.gen1.slash'
  | 'iphone.gen1.slash.circle'
  | 'iphone.gen1.slash.circle.fill'
  | 'iphone.gen2'
  | 'iphone.gen2.badge.play'
  | 'iphone.gen2.circle'
  | 'iphone.gen2.circle.fill'
  | 'iphone.gen2.landscape'
  | 'iphone.gen2.radiowaves.left.and.right'
  | 'iphone.gen2.radiowaves.left.and.right.circle'
  | 'iphone.gen2.radiowaves.left.and.right.circle.fill'
  | 'iphone.gen2.slash'
  | 'iphone.gen2.slash.circle'
  | 'iphone.gen2.slash.circle.fill'
  | 'iphone.gen3'
  | 'iphone.gen3.badge.play'
  | 'iphone.gen3.circle'
  | 'iphone.gen3.circle.fill'
  | 'iphone.gen3.landscape'
  | 'iphone.gen3.radiowaves.left.and.right'
  | 'iphone.gen3.radiowaves.left.and.right.circle'
  | 'iphone.gen3.radiowaves.left.and.right.circle.fill'
  | 'iphone.gen3.slash'
  | 'iphone.gen3.slash.circle'
  | 'iphone.gen3.slash.circle.fill'
  | 'key.horizontal'
  | 'key.horizontal.fill'
  | 'key.radiowaves.forward'
  | 'key.radiowaves.forward.fill'
  | 'kph'
  | 'kph.circle'
  | 'kph.circle.fill'
  | 'lane'
  | 'laptopcomputer.slash'
  | 'light.overhead.left'
  | 'light.overhead.left.fill'
  | 'light.overhead.right'
  | 'light.overhead.right.fill'
  | 'lock.open.rotation'
  | 'macbook.and.ipad'
  | 'macbook.and.iphone'
  | 'minus.plus.and.fluid.batteryblock'
  | 'minus.plus.batteryblock.exclamationmark'
  | 'minus.plus.batteryblock.exclamationmark.fill'
  | 'minus.plus.batteryblock.slash'
  | 'minus.plus.batteryblock.slash.fill'
  | 'minus.plus.batteryblock.stack'
  | 'minus.plus.batteryblock.stack.exclamationmark'
  | 'minus.plus.batteryblock.stack.exclamationmark.fill'
  | 'minus.plus.batteryblock.stack.fill'
  | 'mirror.side.left.and.arrow.turn.down.right'
  | 'mirror.side.left.and.heat.waves'
  | 'mirror.side.right.and.arrow.turn.down.left'
  | 'mirror.side.right.and.heat.waves'
  | 'mountain.2'
  | 'mountain.2.circle'
  | 'mountain.2.circle.fill'
  | 'mountain.2.fill'
  | 'mph'
  | 'mph.circle'
  | 'mph.circle.fill'
  | 'mug'
  | 'mug.fill'
  | 'oilcan'
  | 'oilcan.fill'
  | 'person.crop.circle.dashed'
  | 'play.square.stack'
  | 'play.square.stack.fill'
  | 'questionmark.key.filled'
  | 'retarder.brakesignal'
  | 'road.lane.arrowtriangle.2.inward'
  | 'sailboat.circle'
  | 'sailboat.circle.fill'
  | 'signpost.and.arrowtriangle.up'
  | 'signpost.and.arrowtriangle.up.circle'
  | 'signpost.and.arrowtriangle.up.circle.fill'
  | 'signpost.and.arrowtriangle.up.fill'
  | 'signpost.left.circle'
  | 'signpost.left.circle.fill'
  | 'signpost.right.and.left'
  | 'signpost.right.and.left.circle'
  | 'signpost.right.and.left.circle.fill'
  | 'signpost.right.and.left.fill'
  | 'signpost.right.circle'
  | 'signpost.right.circle.fill'
  | 'snowflake.road.lane'
  | 'snowflake.road.lane.dashed'
  | 'snowflake.slash'
  | 'sos'
  | 'sos.circle'
  | 'sos.circle.fill'
  | 'steeringwheel'
  | 'steeringwheel.and.heat.waves'
  | 'steeringwheel.and.key'
  | 'steeringwheel.and.lock'
  | 'steeringwheel.exclamationmark'
  | 'steeringwheel.road.lane'
  | 'steeringwheel.road.lane.dashed'
  | 'steeringwheel.slash'
  | 'stroller'
  | 'stroller.fill'
  | 'suitcase.rolling'
  | 'suitcase.rolling.fill'
  | 'suv.side'
  | 'suv.side.air.circulate'
  | 'suv.side.air.circulate.fill'
  | 'suv.side.air.fresh'
  | 'suv.side.air.fresh.fill'
  | 'suv.side.and.exclamationmark'
  | 'suv.side.and.exclamationmark.fill'
  | 'suv.side.arrowtriangle.down'
  | 'suv.side.arrowtriangle.down.fill'
  | 'suv.side.arrowtriangle.up'
  | 'suv.side.arrowtriangle.up.arrowtriangle.down'
  | 'suv.side.arrowtriangle.up.arrowtriangle.down.fill'
  | 'suv.side.arrowtriangle.up.fill'
  | 'suv.side.fill'
  | 'suv.side.front.open'
  | 'suv.side.front.open.fill'
  | 'suv.side.rear.open'
  | 'suv.side.rear.open.fill'
  | 'taillight.fog'
  | 'taillight.fog.fill'
  | 'tent.2'
  | 'tent.2.circle'
  | 'tent.2.circle.fill'
  | 'tent.2.fill'
  | 'tent.circle'
  | 'tent.circle.fill'
  | 'thermometer.and.liquid.waves'
  | 'thermometer.transmission'
  | 'toilet.circle'
  | 'toilet.circle.fill'
  | 'transmission'
  | 'tree'
  | 'tree.circle'
  | 'tree.circle.fill'
  | 'tree.fill'
  | 'windshield.front.and.fluid.and.spray'
  | 'windshield.front.and.heat.waves'
  | 'windshield.front.and.spray'
  | 'windshield.front.and.wiper.and.spray'
  | 'windshield.front.and.wiper.exclamationmark'
  | 'windshield.front.and.wiper.intermittent'
  | 'windshield.rear.and.fluid.and.spray'
  | 'windshield.rear.and.heat.waves'
  | 'windshield.rear.and.spray'
  | 'windshield.rear.and.wiper.and.drop'
  | 'windshield.rear.and.wiper.and.spray'
  | 'windshield.rear.and.wiper.exclamationmark'
  | 'windshield.rear.and.wiper.intermittent'
  | 'wrongwaysign'
  | 'wrongwaysign.fill'

/**
 * @name SF Symbols 4.2
 * @description These symbols are available on the following platforms:
 * iOS v16.4+,
 * macOS v13.3+,
 * tvOS v16.4+,
 * visionOS v1.0+,
 * watchOS v9.4+
 */
export type SFSymbols4_2 =
  | SFSymbols4_1
  | '1.lane.ar'
  | '1.lane.hi'
  | '10.lane.ar'
  | '10.lane.hi'
  | '11.lane.ar'
  | '11.lane.hi'
  | '12.lane.ar'
  | '12.lane.hi'
  | '2.lane.ar'
  | '2.lane.hi'
  | '3.lane.ar'
  | '3.lane.hi'
  | '4.lane.ar'
  | '4.lane.hi'
  | '5.lane.ar'
  | '5.lane.hi'
  | '6.lane.ar'
  | '6.lane.hi'
  | '7.lane.ar'
  | '7.lane.hi'
  | '8.lane.ar'
  | '8.lane.hi'
  | '9.lane.ar'
  | '9.lane.hi'
  | 'axle.2'
  | 'axle.2.front.and.rear.engaged'
  | 'axle.2.front.engaged'
  | 'axle.2.rear.engaged'
  | 'beats.powerbeats.left'
  | 'beats.powerbeats.right'
  | 'beats.powerbeats3.left'
  | 'beats.powerbeats3.right'

/**
 * @name SF Symbols 5.0
 * @description These symbols are available on the following platforms:
 * iOS v17.0+,
 * macOS v14.0+,
 * tvOS v17.0+,
 * visionOS v1.0+,
 * watchOS v10.0+
 */
export type SFSymbols5_0 =
  | SFSymbols4_2
  | '2h'
  | '2h.circle'
  | '2h.circle.fill'
  | '4a'
  | '4a.circle'
  | '4a.circle.fill'
  | '4h'
  | '4h.circle'
  | '4h.circle.fill'
  | '4l'
  | '4l.circle'
  | '4l.circle.fill'
  | 'accessibility'
  | 'accessibility.badge.arrow.up.right'
  | 'accessibility.fill'
  | 'airpodspro.chargingcase.wireless.radiowaves.left.and.right'
  | 'airpodspro.chargingcase.wireless.radiowaves.left.and.right.fill'
  | 'apple.terminal'
  | 'apple.terminal.fill'
  | 'apple.terminal.on.rectangle'
  | 'apple.terminal.on.rectangle.fill'
  | 'applepencil.adapter.usb.c'
  | 'applepencil.adapter.usb.c.fill'
  | 'applepencil.and.scribble'
  | 'applepencil.gen1'
  | 'applepencil.gen2'
  | 'applepencil.tip'
  | 'applewatch.and.arrow.forward'
  | 'applewatch.and.arrow.forward.rtl'
  | 'appwindow.swipe.rectangle'
  | 'arcade.stick'
  | 'arcade.stick.and.arrow.down'
  | 'arcade.stick.and.arrow.left'
  | 'arcade.stick.and.arrow.left.and.arrow.right'
  | 'arcade.stick.and.arrow.right'
  | 'arcade.stick.and.arrow.up'
  | 'arcade.stick.and.arrow.up.and.arrow.down'
  | 'arcade.stick.console'
  | 'arcade.stick.console.fill'
  | 'arrow.backward.to.line.square'
  | 'arrow.backward.to.line.square.fill'
  | 'arrow.circlepath'
  | 'arrow.clockwise.square'
  | 'arrow.clockwise.square.fill'
  | 'arrow.counterclockwise.square'
  | 'arrow.counterclockwise.square.fill'
  | 'arrow.down.applewatch'
  | 'arrow.down.backward.and.arrow.up.forward'
  | 'arrow.down.backward.and.arrow.up.forward.circle'
  | 'arrow.down.backward.and.arrow.up.forward.circle.fill'
  | 'arrow.down.backward.and.arrow.up.forward.square'
  | 'arrow.down.backward.and.arrow.up.forward.square.fill'
  | 'arrow.down.backward.toptrailing.rectangle'
  | 'arrow.down.backward.toptrailing.rectangle.fill'
  | 'arrow.down.circle.dotted'
  | 'arrow.down.forward.and.arrow.up.backward.square'
  | 'arrow.down.forward.and.arrow.up.backward.square.fill'
  | 'arrow.down.forward.topleading.rectangle'
  | 'arrow.down.forward.topleading.rectangle.fill'
  | 'arrow.down.left.and.arrow.up.right'
  | 'arrow.down.left.and.arrow.up.right.circle'
  | 'arrow.down.left.and.arrow.up.right.circle.fill'
  | 'arrow.down.left.and.arrow.up.right.square'
  | 'arrow.down.left.and.arrow.up.right.square.fill'
  | 'arrow.down.left.arrow.up.right'
  | 'arrow.down.left.arrow.up.right.circle'
  | 'arrow.down.left.arrow.up.right.circle.fill'
  | 'arrow.down.left.arrow.up.right.square'
  | 'arrow.down.left.arrow.up.right.square.fill'
  | 'arrow.down.left.topright.rectangle'
  | 'arrow.down.left.topright.rectangle.fill'
  | 'arrow.down.right.and.arrow.up.left.square'
  | 'arrow.down.right.and.arrow.up.left.square.fill'
  | 'arrow.down.right.topleft.rectangle'
  | 'arrow.down.right.topleft.rectangle.fill'
  | 'arrow.down.to.line.square'
  | 'arrow.down.to.line.square.fill'
  | 'arrow.forward.to.line.square'
  | 'arrow.forward.to.line.square.fill'
  | 'arrow.left.to.line.square'
  | 'arrow.left.to.line.square.fill'
  | 'arrow.right.to.line.square'
  | 'arrow.right.to.line.square.fill'
  | 'arrow.triangle.2.circlepath.icloud'
  | 'arrow.triangle.2.circlepath.icloud.fill'
  | 'arrow.up.backward.and.arrow.down.forward.square'
  | 'arrow.up.backward.and.arrow.down.forward.square.fill'
  | 'arrow.up.backward.bottomtrailing.rectangle'
  | 'arrow.up.backward.bottomtrailing.rectangle.fill'
  | 'arrow.up.forward.and.arrow.down.backward'
  | 'arrow.up.forward.and.arrow.down.backward.circle'
  | 'arrow.up.forward.and.arrow.down.backward.circle.fill'
  | 'arrow.up.forward.and.arrow.down.backward.square'
  | 'arrow.up.forward.and.arrow.down.backward.square.fill'
  | 'arrow.up.forward.bottomleading.rectangle'
  | 'arrow.up.forward.bottomleading.rectangle.fill'
  | 'arrow.up.left.and.arrow.down.right.square'
  | 'arrow.up.left.and.arrow.down.right.square.fill'
  | 'arrow.up.left.arrow.down.right'
  | 'arrow.up.left.arrow.down.right.circle'
  | 'arrow.up.left.arrow.down.right.circle.fill'
  | 'arrow.up.left.arrow.down.right.square'
  | 'arrow.up.left.arrow.down.right.square.fill'
  | 'arrow.up.left.bottomright.rectangle'
  | 'arrow.up.left.bottomright.rectangle.fill'
  | 'arrow.up.right.and.arrow.down.left'
  | 'arrow.up.right.and.arrow.down.left.circle'
  | 'arrow.up.right.and.arrow.down.left.circle.fill'
  | 'arrow.up.right.and.arrow.down.left.square'
  | 'arrow.up.right.and.arrow.down.left.square.fill'
  | 'arrow.up.right.bottomleft.rectangle'
  | 'arrow.up.right.bottomleft.rectangle.fill'
  | 'arrow.up.to.line.square'
  | 'arrow.up.to.line.square.fill'
  | 'arrow.up.trash'
  | 'arrow.up.trash.fill'
  | 'arrowkeys'
  | 'arrowkeys.down.filled'
  | 'arrowkeys.fill'
  | 'arrowkeys.left.filled'
  | 'arrowkeys.right.filled'
  | 'arrowkeys.up.filled'
  | 'arrowshape.backward.circle'
  | 'arrowshape.backward.circle.fill'
  | 'arrowshape.down'
  | 'arrowshape.down.circle'
  | 'arrowshape.down.circle.fill'
  | 'arrowshape.down.fill'
  | 'arrowshape.forward.circle'
  | 'arrowshape.forward.circle.fill'
  | 'arrowshape.left.arrowshape.right'
  | 'arrowshape.left.arrowshape.right.fill'
  | 'arrowshape.left.circle'
  | 'arrowshape.left.circle.fill'
  | 'arrowshape.right.circle'
  | 'arrowshape.right.circle.fill'
  | 'arrowshape.up'
  | 'arrowshape.up.circle'
  | 'arrowshape.up.circle.fill'
  | 'arrowshape.up.fill'
  | 'arrowtriangle.up.arrowtriangle.down.window.left'
  | 'arrowtriangle.up.arrowtriangle.down.window.right'
  | 'australiandollarsign'
  | 'australiandollarsign.arrow.circlepath'
  | 'australiandollarsign.circle'
  | 'australiandollarsign.circle.fill'
  | 'australiandollarsign.square'
  | 'australiandollarsign.square.fill'
  | 'australsign.arrow.circlepath'
  | 'automatic.brakesignal'
  | 'automatic.headlight.high.beam'
  | 'automatic.headlight.high.beam.fill'
  | 'automatic.headlight.low.beam'
  | 'automatic.headlight.low.beam.fill'
  | 'axle.2.driveshaft.disengaged'
  | 'axle.2.front.disengaged'
  | 'axle.2.rear.disengaged'
  | 'axle.2.rear.lock'
  | 'bahtsign.arrow.circlepath'
  | 'battery.0percent'
  | 'battery.100percent'
  | 'battery.100percent.bolt'
  | 'battery.100percent.bolt.rtl'
  | 'battery.100percent.circle'
  | 'battery.100percent.circle.fill'
  | 'battery.25percent'
  | 'battery.50percent'
  | 'battery.75percent'
  | 'beats.fitpro'
  | 'beats.fitpro.chargingcase'
  | 'beats.fitpro.chargingcase.fill'
  | 'beats.fitpro.left'
  | 'beats.fitpro.right'
  | 'beats.studiobudsplus'
  | 'beats.studiobudsplus.chargingcase'
  | 'beats.studiobudsplus.chargingcase.fill'
  | 'beats.studiobudsplus.left'
  | 'beats.studiobudsplus.right'
  | 'bell.badge.slash'
  | 'bell.badge.slash.fill'
  | 'bell.badge.slash.fill.rtl'
  | 'bell.badge.slash.rtl'
  | 'bell.badge.waveform'
  | 'bell.badge.waveform.fill'
  | 'binoculars.circle'
  | 'binoculars.circle.fill'
  | 'bird.circle'
  | 'bird.circle.fill'
  | 'bitcoinsign.arrow.circlepath'
  | 'bolt.badge.automatic'
  | 'bolt.badge.automatic.fill'
  | 'bolt.badge.checkmark'
  | 'bolt.badge.checkmark.fill'
  | 'bolt.badge.xmark'
  | 'bolt.badge.xmark.fill'
  | 'book.and.wrench'
  | 'book.and.wrench.fill'
  | 'book.pages'
  | 'book.pages.fill'
  | 'brain.fill'
  | 'brain.filled.head.profile'
  | 'brain.head.profile.fill'
  | 'brazilianrealsign.arrow.circlepath'
  | 'bubble'
  | 'bubble.circle'
  | 'bubble.circle.fill'
  | 'bubble.fill'
  | 'bubble.left.and.text.bubble.right'
  | 'bubble.left.and.text.bubble.right.fill'
  | 'bubble.left.and.text.bubble.right.fill.rtl'
  | 'bubble.left.and.text.bubble.right.rtl'
  | 'button.angledbottom.horizontal.left'
  | 'button.angledbottom.horizontal.left.fill'
  | 'button.angledbottom.horizontal.right'
  | 'button.angledbottom.horizontal.right.fill'
  | 'button.angledtop.vertical.left'
  | 'button.angledtop.vertical.left.fill'
  | 'button.angledtop.vertical.right'
  | 'button.angledtop.vertical.right.fill'
  | 'button.horizontal'
  | 'button.horizontal.fill'
  | 'button.horizontal.top.press'
  | 'button.horizontal.top.press.fill'
  | 'button.roundedbottom.horizontal'
  | 'button.roundedbottom.horizontal.fill'
  | 'button.roundedtop.horizontal'
  | 'button.roundedtop.horizontal.fill'
  | 'button.vertical.left.press'
  | 'button.vertical.left.press.fill'
  | 'button.vertical.right.press'
  | 'button.vertical.right.press.fill'
  | 'cable.coaxial'
  | 'cable.connector.slash'
  | 'calendar.badge.checkmark'
  | 'calendar.badge.checkmark.rtl'
  | 'camera.badge.clock'
  | 'camera.badge.clock.fill'
  | 'camera.badge.ellipsis.fill'
  | 'car.front.waves.down'
  | 'car.front.waves.down.fill'
  | 'car.rear.and.collision.road.lane'
  | 'car.rear.and.collision.road.lane.slash'
  | 'car.side.hill.down'
  | 'car.side.hill.down.fill'
  | 'car.side.hill.up'
  | 'car.side.hill.up.fill'
  | 'car.side.lock'
  | 'car.side.lock.fill'
  | 'car.side.lock.open'
  | 'car.side.lock.open.fill'
  | 'car.side.rear.and.collision.and.car.side.front'
  | 'car.side.rear.and.collision.and.car.side.front.slash'
  | 'car.side.rear.and.exclamationmark.and.car.side.front'
  | 'car.side.rear.and.wave.3.and.car.side.front'
  | 'car.top.door.sliding.left.open'
  | 'car.top.door.sliding.left.open.fill'
  | 'car.top.door.sliding.right.open'
  | 'car.top.door.sliding.right.open.fill'
  | 'car.top.frontleft.arrowtriangle'
  | 'car.top.frontleft.arrowtriangle.fill'
  | 'car.top.frontright.arrowtriangle'
  | 'car.top.frontright.arrowtriangle.fill'
  | 'car.top.radiowaves.rear.right.badge.exclamationmark'
  | 'car.top.radiowaves.rear.right.badge.exclamationmark.fill'
  | 'car.top.radiowaves.rear.right.badge.xmark'
  | 'car.top.radiowaves.rear.right.badge.xmark.fill'
  | 'car.top.rearleft.arrowtriangle'
  | 'car.top.rearleft.arrowtriangle.fill'
  | 'car.top.rearright.arrowtriangle'
  | 'car.top.rearright.arrowtriangle.fill'
  | 'car.window.left'
  | 'car.window.left.badge.exclamationmark'
  | 'car.window.left.badge.xmark'
  | 'car.window.left.exclamationmark'
  | 'car.window.left.xmark'
  | 'car.window.right'
  | 'car.window.right.badge.exclamationmark'
  | 'car.window.right.badge.xmark'
  | 'car.window.right.exclamationmark'
  | 'car.window.right.xmark'
  | 'carseat.left'
  | 'carseat.left.1'
  | 'carseat.left.1.fill'
  | 'carseat.left.2'
  | 'carseat.left.2.fill'
  | 'carseat.left.3'
  | 'carseat.left.3.fill'
  | 'carseat.left.and.heat.waves'
  | 'carseat.left.and.heat.waves.fill'
  | 'carseat.left.backrest.up.and.down'
  | 'carseat.left.backrest.up.and.down.fill'
  | 'carseat.left.fan'
  | 'carseat.left.fan.fill'
  | 'carseat.left.fill'
  | 'carseat.left.forward.and.backward'
  | 'carseat.left.forward.and.backward.fill'
  | 'carseat.left.massage'
  | 'carseat.left.massage.fill'
  | 'carseat.left.up.and.down'
  | 'carseat.left.up.and.down.fill'
  | 'carseat.right'
  | 'carseat.right.1'
  | 'carseat.right.1.fill'
  | 'carseat.right.2'
  | 'carseat.right.2.fill'
  | 'carseat.right.3'
  | 'carseat.right.3.fill'
  | 'carseat.right.and.heat.waves'
  | 'carseat.right.and.heat.waves.fill'
  | 'carseat.right.backrest.up.and.down'
  | 'carseat.right.backrest.up.and.down.fill'
  | 'carseat.right.fan'
  | 'carseat.right.fan.fill'
  | 'carseat.right.fill'
  | 'carseat.right.forward.and.backward'
  | 'carseat.right.forward.and.backward.fill'
  | 'carseat.right.massage'
  | 'carseat.right.massage.fill'
  | 'carseat.right.up.and.down'
  | 'carseat.right.up.and.down.fill'
  | 'cat'
  | 'cat.circle'
  | 'cat.circle.fill'
  | 'cat.fill'
  | 'cedisign.arrow.circlepath'
  | 'centsign.arrow.circlepath'
  | 'character.magnify'
  | 'character.magnify.ar'
  | 'character.magnify.he'
  | 'character.magnify.hi'
  | 'character.magnify.ja'
  | 'character.magnify.ko'
  | 'character.magnify.th'
  | 'character.magnify.zh'
  | 'chart.bar.xaxis.ascending'
  | 'chart.bar.xaxis.ascending.badge.clock'
  | 'chart.bar.xaxis.ascending.badge.clock.rtl'
  | 'checkmark.applewatch'
  | 'checkmark.bubble.fill.rtl'
  | 'checkmark.bubble.rtl'
  | 'checkmark.gobackward'
  | 'checkmark.rectangle.stack'
  | 'checkmark.rectangle.stack.fill'
  | 'chineseyuanrenminbisign'
  | 'chineseyuanrenminbisign.arrow.circlepath'
  | 'chineseyuanrenminbisign.circle'
  | 'chineseyuanrenminbisign.circle.fill'
  | 'chineseyuanrenminbisign.square'
  | 'chineseyuanrenminbisign.square.fill'
  | 'circle.badge.checkmark'
  | 'circle.badge.checkmark.fill'
  | 'circle.badge.exclamationmark'
  | 'circle.badge.exclamationmark.fill'
  | 'circle.badge.minus'
  | 'circle.badge.minus.fill'
  | 'circle.badge.plus'
  | 'circle.badge.plus.fill'
  | 'circle.badge.questionmark'
  | 'circle.badge.questionmark.ar'
  | 'circle.badge.questionmark.fill'
  | 'circle.badge.questionmark.fill.ar'
  | 'circle.badge.xmark'
  | 'circle.badge.xmark.fill'
  | 'circle.bottomhalf.filled.inverse'
  | 'circle.bottomrighthalf.checkered'
  | 'circle.dotted.and.circle'
  | 'circle.dotted.circle'
  | 'circle.dotted.circle.fill'
  | 'circle.filled.ipad'
  | 'circle.filled.ipad.fill'
  | 'circle.filled.ipad.landscape'
  | 'circle.filled.ipad.landscape.fill'
  | 'circle.filled.iphone'
  | 'circle.filled.iphone.fill'
  | 'circle.lefthalf.filled.inverse'
  | 'circle.lefthalf.filled.righthalf.striped.horizontal'
  | 'circle.lefthalf.filled.righthalf.striped.horizontal.inverse'
  | 'circle.lefthalf.striped.horizontal'
  | 'circle.lefthalf.striped.horizontal.inverse'
  | 'circle.righthalf.filled.inverse'
  | 'circle.tophalf.filled.inverse'
  | 'cloud.rainbow.half'
  | 'cloud.rainbow.half.fill'
  | 'coloncurrencysign.arrow.circlepath'
  | 'creditcard.trianglebadge.exclamationmark.fill'
  | 'cruzeirosign.arrow.circlepath'
  | 'cursorarrow.slash'
  | 'cursorarrow.slash.square'
  | 'cursorarrow.slash.square.fill'
  | 'danishkronesign'
  | 'danishkronesign.arrow.circlepath'
  | 'danishkronesign.circle'
  | 'danishkronesign.circle.fill'
  | 'danishkronesign.square'
  | 'danishkronesign.square.fill'
  | 'dishwasher.circle'
  | 'dishwasher.circle.fill'
  | 'doc.badge.clock'
  | 'doc.badge.clock.fill'
  | 'doc.questionmark'
  | 'doc.questionmark.ar'
  | 'doc.questionmark.fill'
  | 'doc.questionmark.fill.ar'
  | 'doc.questionmark.fill.rtl'
  | 'doc.questionmark.rtl'
  | 'dog'
  | 'dog.circle'
  | 'dog.circle.fill'
  | 'dog.fill'
  | 'dongsign.arrow.circlepath'
  | 'dot.scope'
  | 'dot.scope.display'
  | 'dot.scope.laptopcomputer'
  | 'drop.halffull'
  | 'drop.transmission'
  | 'dryer.circle'
  | 'dryer.circle.fill'
  | 'ear.badge.waveform'
  | 'engine.combustion.badge.exclamationmark'
  | 'engine.combustion.badge.exclamationmark.fill'
  | 'envelope.badge.person.crop'
  | 'envelope.badge.person.crop.fill'
  | 'eurosign.arrow.circlepath'
  | 'eurozonesign'
  | 'eurozonesign.arrow.circlepath'
  | 'eurozonesign.circle'
  | 'eurozonesign.circle.fill'
  | 'eurozonesign.square'
  | 'eurozonesign.square.fill'
  | 'ev.charger'
  | 'ev.charger.arrowtriangle.left'
  | 'ev.charger.arrowtriangle.left.fill'
  | 'ev.charger.arrowtriangle.right'
  | 'ev.charger.arrowtriangle.right.fill'
  | 'ev.charger.exclamationmark'
  | 'ev.charger.exclamationmark.fill'
  | 'ev.charger.fill'
  | 'ev.charger.slash'
  | 'ev.charger.slash.fill'
  | 'ev.plug.ac.gb.t'
  | 'ev.plug.ac.gb.t.fill'
  | 'ev.plug.ac.type.1'
  | 'ev.plug.ac.type.1.fill'
  | 'ev.plug.ac.type.2'
  | 'ev.plug.ac.type.2.fill'
  | 'ev.plug.dc.ccs1'
  | 'ev.plug.dc.ccs1.fill'
  | 'ev.plug.dc.ccs2'
  | 'ev.plug.dc.ccs2.fill'
  | 'ev.plug.dc.chademo'
  | 'ev.plug.dc.chademo.fill'
  | 'ev.plug.dc.gb.t'
  | 'ev.plug.dc.gb.t.fill'
  | 'ev.plug.dc.nacs'
  | 'ev.plug.dc.nacs.fill'
  | 'exclamationmark.magnifyingglass'
  | 'exclamationmark.tirepressure'
  | 'exclamationmark.warninglight'
  | 'exclamationmark.warninglight.fill'
  | 'eyeglasses.slash'
  | 'fan'
  | 'fan.badge.automatic'
  | 'fan.badge.automatic.fill'
  | 'fan.fill'
  | 'fan.slash'
  | 'fan.slash.fill'
  | 'field.of.view.ultrawide'
  | 'field.of.view.ultrawide.fill'
  | 'field.of.view.wide'
  | 'field.of.view.wide.fill'
  | 'figure'
  | 'figure.2'
  | 'figure.2.circle'
  | 'figure.2.circle.fill'
  | 'figure.child'
  | 'figure.child.and.lock'
  | 'figure.child.and.lock.fill'
  | 'figure.child.and.lock.open'
  | 'figure.child.and.lock.open.fill'
  | 'figure.child.circle'
  | 'figure.child.circle.fill'
  | 'figure.seated.side'
  | 'figure.seated.side.air.distribution.lower'
  | 'figure.seated.side.air.distribution.middle'
  | 'figure.seated.side.air.distribution.middle.and.lower'
  | 'figure.seated.side.air.distribution.middle.and.lower.angled'
  | 'figure.seated.side.air.distribution.upper'
  | 'figure.seated.side.air.distribution.upper.angled.and.lower.angled'
  | 'figure.seated.side.air.distribution.upper.angled.and.middle'
  | 'figure.seated.side.air.distribution.upper.angled.and.middle.and.lower.angled'
  | 'figure.seated.side.automatic'
  | 'figure.seated.side.windshield.front.and.heat.waves.air.distribution.lower'
  | 'figure.seated.side.windshield.front.and.heat.waves.air.distribution.middle'
  | 'figure.seated.side.windshield.front.and.heat.waves.air.distribution.middle.and.lower'
  | 'figure.seated.side.windshield.front.and.heat.waves.air.distribution.upper'
  | 'figure.seated.side.windshield.front.and.heat.waves.air.distribution.upper.and.lower'
  | 'figure.seated.side.windshield.front.and.heat.waves.air.distribution.upper.and.middle'
  | 'figure.seated.side.windshield.front.and.heat.waves.air.distribution.upper.and.middle.and.lower'
  | 'figure.walk.motion.trianglebadge.exclamationmark'
  | 'fireworks'
  | 'flashlight.off.circle'
  | 'flashlight.off.circle.fill'
  | 'flashlight.on.circle'
  | 'flashlight.on.circle.fill'
  | 'flashlight.slash'
  | 'flashlight.slash.circle'
  | 'flashlight.slash.circle.fill'
  | 'flask'
  | 'flask.fill'
  | 'florinsign.arrow.circlepath'
  | 'francsign.arrow.circlepath'
  | 'fuelpump.arrowtriangle.left'
  | 'fuelpump.arrowtriangle.left.fill'
  | 'fuelpump.arrowtriangle.right'
  | 'fuelpump.arrowtriangle.right.fill'
  | 'fuelpump.exclamationmark'
  | 'fuelpump.exclamationmark.fill'
  | 'fuelpump.slash'
  | 'fuelpump.slash.fill'
  | 'gauge.open.with.lines.needle.33percent'
  | 'gauge.open.with.lines.needle.33percent.and.arrowtriangle'
  | 'gauge.open.with.lines.needle.33percent.and.arrowtriangle.from.0percent.to.50percent'
  | 'gauge.open.with.lines.needle.67percent.and.arrowtriangle'
  | 'gauge.open.with.lines.needle.67percent.and.arrowtriangle.and.car'
  | 'gauge.open.with.lines.needle.84percent.exclamation'
  | 'gauge.with.dots.needle.0percent'
  | 'gauge.with.dots.needle.100percent'
  | 'gauge.with.dots.needle.33percent'
  | 'gauge.with.dots.needle.50percent'
  | 'gauge.with.dots.needle.67percent'
  | 'gauge.with.dots.needle.bottom.0percent'
  | 'gauge.with.dots.needle.bottom.100percent'
  | 'gauge.with.dots.needle.bottom.50percent'
  | 'gauge.with.dots.needle.bottom.50percent.badge.minus'
  | 'gauge.with.dots.needle.bottom.50percent.badge.plus'
  | 'gauge.with.needle'
  | 'gauge.with.needle.fill'
  | 'gearshift.layout.sixspeed'
  | 'guaranisign.arrow.circlepath'
  | 'gym.bag'
  | 'gym.bag.fill'
  | 'hand.point.up.left.and.text'
  | 'hand.point.up.left.and.text.fill'
  | 'handbag.circle'
  | 'handbag.circle.fill'
  | 'hands.and.sparkles'
  | 'hands.and.sparkles.fill'
  | 'hanger'
  | 'hare.circle'
  | 'hare.circle.fill'
  | 'head.profile.arrow.forward.and.visionpro'
  | 'heat.waves'
  | 'horn'
  | 'horn.blast'
  | 'horn.blast.fill'
  | 'horn.fill'
  | 'hourglass.and.lock'
  | 'hryvniasign.arrow.circlepath'
  | 'indianrupeesign.arrow.circlepath'
  | 'info.bubble.fill.rtl'
  | 'info.bubble.rtl'
  | 'ipad.case'
  | 'ipad.case.and.iphone.case'
  | 'ipad.sizes'
  | 'iphone.and.arrow.left.and.arrow.right'
  | 'iphone.case'
  | 'iphone.sizes'
  | 'kashida.arabic'
  | 'key.radiowaves.forward.slash'
  | 'key.radiowaves.forward.slash.fill'
  | 'key.slash'
  | 'key.slash.fill'
  | 'kipsign.arrow.circlepath'
  | 'l.button.roundedbottom.horizontal'
  | 'l.button.roundedbottom.horizontal.fill'
  | 'l1.button.roundedbottom.horizontal'
  | 'l1.button.roundedbottom.horizontal.fill'
  | 'l1.circle'
  | 'l1.circle.fill'
  | 'l2.button.angledtop.vertical.left'
  | 'l2.button.angledtop.vertical.left.fill'
  | 'l2.button.roundedtop.horizontal'
  | 'l2.button.roundedtop.horizontal.fill'
  | 'l2.circle'
  | 'l2.circle.fill'
  | 'l3.button.angledbottom.horizontal.left'
  | 'l3.button.angledbottom.horizontal.left.fill'
  | 'l4.button.horizontal'
  | 'l4.button.horizontal.fill'
  | 'ladybug.circle'
  | 'ladybug.circle.fill'
  | 'larisign.arrow.circlepath'
  | 'laser.burst'
  | 'lasso.badge.sparkles'
  | 'lb.button.roundedbottom.horizontal'
  | 'lb.button.roundedbottom.horizontal.fill'
  | 'lb.circle'
  | 'lb.circle.fill'
  | 'left'
  | 'left.circle'
  | 'left.circle.fill'
  | 'licenseplate'
  | 'licenseplate.fill'
  | 'lightbulb.max'
  | 'lightbulb.max.fill'
  | 'lightbulb.min'
  | 'lightbulb.min.badge.exclamationmark'
  | 'lightbulb.min.badge.exclamationmark.fill'
  | 'lightbulb.min.fill'
  | 'lightrail'
  | 'lightrail.fill'
  | 'lightspectrum.horizontal'
  | 'line.3.horizontal.button.angledtop.vertical.right'
  | 'line.3.horizontal.button.angledtop.vertical.right.fill'
  | 'lines.measurement.vertical'
  | 'lirasign.arrow.circlepath'
  | 'livephoto.badge.automatic'
  | 'lizard.circle'
  | 'lizard.circle.fill'
  | 'lm.button.horizontal'
  | 'lm.button.horizontal.fill'
  | 'lock.app.dashed'
  | 'lock.badge.clock'
  | 'lock.badge.clock.fill'
  | 'lock.circle.dotted'
  | 'lsb.button.angledbottom.horizontal.left'
  | 'lsb.button.angledbottom.horizontal.left.fill'
  | 'lt.button.roundedtop.horizontal'
  | 'lt.button.roundedtop.horizontal.fill'
  | 'lt.circle'
  | 'lt.circle.fill'
  | 'm1.button.horizontal'
  | 'm1.button.horizontal.fill'
  | 'm2.button.horizontal'
  | 'm2.button.horizontal.fill'
  | 'm3.button.horizontal'
  | 'm3.button.horizontal.fill'
  | 'm4.button.horizontal'
  | 'm4.button.horizontal.fill'
  | 'macbook'
  | 'macbook.and.visionpro'
  | 'macbook.gen1'
  | 'macbook.gen2'
  | 'macwindow.and.cursorarrow'
  | 'macwindow.and.cursorarrow.rtl'
  | 'manatsign.arrow.circlepath'
  | 'mappin.and.ellipse.circle'
  | 'mappin.and.ellipse.circle.fill'
  | 'message.badge.waveform'
  | 'message.badge.waveform.fill'
  | 'millsign.arrow.circlepath'
  | 'moon.dust'
  | 'moon.dust.circle'
  | 'moon.dust.circle.fill'
  | 'moon.dust.fill'
  | 'moonrise'
  | 'moonrise.circle'
  | 'moonrise.circle.fill'
  | 'moonrise.fill'
  | 'moonset'
  | 'moonset.circle'
  | 'moonset.circle.fill'
  | 'moonset.fill'
  | 'movieclapper'
  | 'movieclapper.fill'
  | 'nairasign.arrow.circlepath'
  | 'network.slash'
  | 'norwegiankronesign'
  | 'norwegiankronesign.arrow.circlepath'
  | 'norwegiankronesign.circle'
  | 'norwegiankronesign.circle.fill'
  | 'norwegiankronesign.square'
  | 'norwegiankronesign.square.fill'
  | 'opticid'
  | 'opticid.fill'
  | 'p1.button.horizontal'
  | 'p1.button.horizontal.fill'
  | 'p2.button.horizontal'
  | 'p2.button.horizontal.fill'
  | 'p3.button.horizontal'
  | 'p3.button.horizontal.fill'
  | 'p4.button.horizontal'
  | 'p4.button.horizontal.fill'
  | 'paddleshifter.left'
  | 'paddleshifter.left.fill'
  | 'paddleshifter.right'
  | 'paddleshifter.right.fill'
  | 'pano.badge.play'
  | 'pano.badge.play.fill'
  | 'parkingsign.radiowaves.left.and.right'
  | 'parkingsign.radiowaves.right.and.safetycone'
  | 'parkingsign.steeringwheel'
  | 'pedal.accelerator'
  | 'pedal.accelerator.fill'
  | 'pedal.brake'
  | 'pedal.brake.fill'
  | 'pedal.clutch'
  | 'pedal.clutch.fill'
  | 'pencil.and.list.clipboard'
  | 'pencil.and.list.clipboard.rtl'
  | 'pencil.and.scribble'
  | 'pencil.tip.crop.circle.badge.arrow.forward.fill'
  | 'pencil.tip.crop.circle.badge.minus.fill'
  | 'pencil.tip.crop.circle.badge.plus.fill'
  | 'pencil.tip.crop.circle.fill'
  | 'person.2.badge.key'
  | 'person.2.badge.key.fill'
  | 'person.and.background.striped.horizontal'
  | 'person.bubble'
  | 'person.bubble.fill'
  | 'person.bust.circle'
  | 'person.bust.circle.fill'
  | 'person.crop.circle.dashed.circle'
  | 'person.crop.circle.dashed.circle.fill'
  | 'person.slash'
  | 'person.slash.fill'
  | 'pesetasign.arrow.circlepath'
  | 'pesosign.arrow.circlepath'
  | 'phone.badge.waveform'
  | 'phone.badge.waveform.fill'
  | 'phone.bubble'
  | 'phone.bubble.fill'
  | 'phone.bubble.fill.rtl'
  | 'phone.bubble.rtl'
  | 'photo.artframe.circle'
  | 'photo.artframe.circle.fill'
  | 'photo.badge.arrow.down'
  | 'photo.badge.arrow.down.fill'
  | 'photo.badge.checkmark'
  | 'photo.badge.checkmark.fill'
  | 'photo.badge.plus'
  | 'photo.badge.plus.fill'
  | 'play.house'
  | 'play.house.fill'
  | 'point.bottomleft.filled.forward.to.point.topright.scurvepath'
  | 'point.bottomleft.forward.to.arrowtriangle.uturn.scurvepath'
  | 'point.bottomleft.forward.to.arrowtriangle.uturn.scurvepath.fill'
  | 'point.bottomleft.forward.to.point.topright.filled.scurvepath'
  | 'point.bottomleft.forward.to.point.topright.scurvepath'
  | 'point.bottomleft.forward.to.point.topright.scurvepath.fill'
  | 'point.forward.to.point.capsulepath'
  | 'point.forward.to.point.capsulepath.fill'
  | 'point.topleft.down.to.point.bottomright.curvepath'
  | 'point.topleft.down.to.point.bottomright.curvepath.fill'
  | 'point.topleft.down.to.point.bottomright.filled.curvepath'
  | 'point.topleft.filled.down.to.point.bottomright.curvepath'
  | 'polishzlotysign'
  | 'polishzlotysign.arrow.circlepath'
  | 'polishzlotysign.circle'
  | 'polishzlotysign.circle.fill'
  | 'polishzlotysign.square'
  | 'polishzlotysign.square.fill'
  | 'powercord'
  | 'powercord.fill'
  | 'r.button.roundedbottom.horizontal'
  | 'r.button.roundedbottom.horizontal.fill'
  | 'r1.button.roundedbottom.horizontal'
  | 'r1.button.roundedbottom.horizontal.fill'
  | 'r1.circle'
  | 'r1.circle.fill'
  | 'r2.button.angledtop.vertical.right'
  | 'r2.button.angledtop.vertical.right.fill'
  | 'r2.button.roundedtop.horizontal'
  | 'r2.button.roundedtop.horizontal.fill'
  | 'r2.circle'
  | 'r2.circle.fill'
  | 'r3.button.angledbottom.horizontal.right'
  | 'r3.button.angledbottom.horizontal.right.fill'
  | 'r4.button.horizontal'
  | 'r4.button.horizontal.fill'
  | 'rainbow'
  | 'rb.button.roundedbottom.horizontal'
  | 'rb.button.roundedbottom.horizontal.fill'
  | 'rb.circle'
  | 'rb.circle.fill'
  | 'rectangle.3.group.bubble'
  | 'rectangle.3.group.bubble.fill'
  | 'rectangle.checkered'
  | 'rectangle.inset.filled.and.cursorarrow'
  | 'rectangle.inset.filled.badge.record'
  | 'rectangle.landscape.rotate'
  | 'rectangle.on.rectangle.badge.gearshape'
  | 'rectangle.on.rectangle.button.angledtop.vertical.left'
  | 'rectangle.on.rectangle.button.angledtop.vertical.left.fill'
  | 'rectangle.portrait.badge.plus'
  | 'rectangle.portrait.badge.plus.fill'
  | 'rectangle.portrait.rotate'
  | 'rectangle.ratio.16.to.9'
  | 'rectangle.ratio.16.to.9.fill'
  | 'rectangle.ratio.3.to.4'
  | 'rectangle.ratio.3.to.4.fill'
  | 'rectangle.ratio.4.to.3'
  | 'rectangle.ratio.4.to.3.fill'
  | 'rectangle.ratio.9.to.16'
  | 'rectangle.ratio.9.to.16.fill'
  | 'retarder.brakesignal.and.exclamationmark'
  | 'retarder.brakesignal.slash'
  | 'right'
  | 'right.circle'
  | 'right.circle.fill'
  | 'righttriangle'
  | 'righttriangle.fill'
  | 'righttriangle.split.diagonal'
  | 'righttriangle.split.diagonal.fill'
  | 'rm.button.horizontal'
  | 'rm.button.horizontal.fill'
  | 'rotate.3d.circle'
  | 'rotate.3d.circle.fill'
  | 'rotate.3d.fill'
  | 'rsb.button.angledbottom.horizontal.right'
  | 'rsb.button.angledbottom.horizontal.right.fill'
  | 'rt.button.roundedtop.horizontal'
  | 'rt.button.roundedtop.horizontal.fill'
  | 'rt.circle'
  | 'rt.circle.fill'
  | 'rublesign.arrow.circlepath'
  | 'rupeesign.arrow.circlepath'
  | 'shared.with.you.circle'
  | 'shared.with.you.circle.fill'
  | 'shekelsign.arrow.circlepath'
  | 'shield.checkered'
  | 'shield.lefthalf.filled.badge.checkmark'
  | 'shield.lefthalf.filled.trianglebadge.exclamationmark'
  | 'shoe'
  | 'shoe.2'
  | 'shoe.2.fill'
  | 'shoe.circle'
  | 'shoe.circle.fill'
  | 'shoe.fill'
  | 'skateboard'
  | 'skateboard.fill'
  | 'skis'
  | 'skis.fill'
  | 'slider.horizontal.2.square'
  | 'slider.horizontal.below.sun.max'
  | 'smartphone'
  | 'snowboard'
  | 'snowboard.fill'
  | 'speaker.wave.2.bubble'
  | 'speaker.wave.2.bubble.fill'
  | 'speaker.wave.2.bubble.fill.rtl'
  | 'speaker.wave.2.bubble.rtl'
  | 'square.2.layers.3d.fill'
  | 'square.arrowtriangle.4.outward'
  | 'square.badge.plus'
  | 'square.badge.plus.fill'
  | 'square.resize'
  | 'square.resize.down'
  | 'square.resize.up'
  | 'square.stack.3d.up.badge.automatic'
  | 'square.stack.3d.up.badge.automatic.fill'
  | 'square.stack.3d.up.trianglebadge.exclamationmark'
  | 'square.stack.3d.up.trianglebadge.exclamationmark.fill'
  | 'squares.leading.rectangle.fill'
  | 'squareshape.dotted.squareshape'
  | 'squareshape.squareshape.dotted'
  | 'staroflife.shield'
  | 'staroflife.shield.fill'
  | 'steeringwheel.and.liquid.wave'
  | 'steeringwheel.arrowtriangle.left'
  | 'steeringwheel.arrowtriangle.right'
  | 'steeringwheel.badge.exclamationmark'
  | 'steeringwheel.circle'
  | 'steeringwheel.circle.fill'
  | 'sterlingsign.arrow.circlepath'
  | 'storefront'
  | 'storefront.circle'
  | 'storefront.circle.fill'
  | 'storefront.fill'
  | 'sun.horizon'
  | 'sun.horizon.circle'
  | 'sun.horizon.circle.fill'
  | 'sun.horizon.fill'
  | 'sun.rain'
  | 'sun.rain.circle'
  | 'sun.rain.circle.fill'
  | 'sun.rain.fill'
  | 'sun.snow'
  | 'sun.snow.circle'
  | 'sun.snow.circle.fill'
  | 'sun.snow.fill'
  | 'sunglasses'
  | 'sunglasses.fill'
  | 'surfboard'
  | 'surfboard.fill'
  | 'suv.side.hill.down'
  | 'suv.side.hill.down.fill'
  | 'suv.side.hill.up'
  | 'suv.side.hill.up.fill'
  | 'suv.side.lock'
  | 'suv.side.lock.fill'
  | 'suv.side.lock.open'
  | 'suv.side.lock.open.fill'
  | 'swedishkronasign'
  | 'swedishkronasign.arrow.circlepath'
  | 'swedishkronasign.circle'
  | 'swedishkronasign.circle.fill'
  | 'swedishkronasign.square'
  | 'swedishkronasign.square.fill'
  | 'swiftdata'
  | 'swirl.circle.righthalf.filled'
  | 'swirl.circle.righthalf.filled.inverse'
  | 'tengesign.arrow.circlepath'
  | 'textformat.12.km'
  | 'textformat.12.my'
  | 'thermometer.variable.and.figure'
  | 'thermometer.variable.and.figure.circle'
  | 'thermometer.variable.and.figure.circle.fill'
  | 'tirepressure'
  | 'tortoise.circle'
  | 'tortoise.circle.fill'
  | 'traction.control.tirepressure'
  | 'traction.control.tirepressure.exclamationmark'
  | 'traction.control.tirepressure.slash'
  | 'triangleshape'
  | 'triangleshape.fill'
  | 'truck.box'
  | 'truck.box.badge.clock'
  | 'truck.box.badge.clock.fill'
  | 'truck.box.badge.clock.fill.rtl'
  | 'truck.box.badge.clock.rtl'
  | 'truck.box.fill'
  | 'truck.pickup.side'
  | 'truck.pickup.side.air.circulate'
  | 'truck.pickup.side.air.circulate.fill'
  | 'truck.pickup.side.air.fresh'
  | 'truck.pickup.side.air.fresh.fill'
  | 'truck.pickup.side.and.exclamationmark'
  | 'truck.pickup.side.and.exclamationmark.fill'
  | 'truck.pickup.side.arrowtriangle.down'
  | 'truck.pickup.side.arrowtriangle.down.fill'
  | 'truck.pickup.side.arrowtriangle.up'
  | 'truck.pickup.side.arrowtriangle.up.arrowtriangle.down'
  | 'truck.pickup.side.arrowtriangle.up.arrowtriangle.down.fill'
  | 'truck.pickup.side.arrowtriangle.up.fill'
  | 'truck.pickup.side.fill'
  | 'truck.pickup.side.front.open'
  | 'truck.pickup.side.front.open.fill'
  | 'truck.pickup.side.hill.down'
  | 'truck.pickup.side.hill.down.fill'
  | 'truck.pickup.side.hill.up'
  | 'truck.pickup.side.hill.up.fill'
  | 'truck.pickup.side.lock'
  | 'truck.pickup.side.lock.fill'
  | 'truck.pickup.side.lock.open'
  | 'truck.pickup.side.lock.open.fill'
  | 'tshirt.circle'
  | 'tshirt.circle.fill'
  | 'tugriksign.arrow.circlepath'
  | 'turkishlirasign.arrow.circlepath'
  | 'tv.badge.wifi'
  | 'tv.badge.wifi.fill'
  | 'tv.slash'
  | 'tv.slash.fill'
  | 'video.badge.waveform'
  | 'video.badge.waveform.fill'
  | 'video.bubble'
  | 'video.bubble.fill'
  | 'video.bubble.fill.rtl'
  | 'video.bubble.rtl'
  | 'video.slash.circle'
  | 'video.slash.circle.fill'
  | 'viewfinder.rectangular'
  | 'viewfinder.trianglebadge.exclamationmark'
  | 'visionpro'
  | 'visionpro.and.arrow.forward'
  | 'visionpro.and.arrow.forward.fill'
  | 'visionpro.badge.exclamationmark'
  | 'visionpro.badge.exclamationmark.fill'
  | 'visionpro.badge.play'
  | 'visionpro.badge.play.fill'
  | 'visionpro.circle'
  | 'visionpro.circle.fill'
  | 'visionpro.fill'
  | 'visionpro.slash'
  | 'visionpro.slash.circle'
  | 'visionpro.slash.circle.fill'
  | 'visionpro.slash.fill'
  | 'voiceover'
  | 'warninglight'
  | 'warninglight.fill'
  | 'washer.circle'
  | 'washer.circle.fill'
  | 'watch.analog'
  | 'waterbottle'
  | 'waterbottle.fill'
  | 'waveform.and.person.filled'
  | 'waveform.badge.magnifyingglass'
  | 'waveform.badge.mic'
  | 'wifi.exclamationmark.circle'
  | 'wifi.exclamationmark.circle.fill'
  | 'wonsign.arrow.circlepath'
  | 'xserve.raid'
  | 'yensign.arrow.circlepath'
  | 'yieldsign'
  | 'yieldsign.fill'
  | 'zl.button.roundedtop.horizontal'
  | 'zl.button.roundedtop.horizontal.fill'
  | 'zr.button.roundedtop.horizontal'
  | 'zr.button.roundedtop.horizontal.fill'

/**
 * @name SF Symbols 5.1
 * @description These symbols are available on the following platforms:
 * iOS v17.2+,
 * macOS v14.2+,
 * tvOS v17.2+,
 * visionOS v1.1+,
 * watchOS v10.2+
 */
export type SFSymbols5_1 =
  | SFSymbols5_0
  | '0.circle.fill.hi'
  | '0.circle.hi'
  | '0.square.fill.hi'
  | '0.square.hi'
  | '00.circle.ar'
  | '00.circle.fill.ar'
  | '00.circle.fill.hi'
  | '00.circle.hi'
  | '00.square.ar'
  | '00.square.fill.ar'
  | '00.square.fill.hi'
  | '00.square.hi'
  | '01.circle.ar'
  | '01.circle.fill.ar'
  | '01.circle.fill.hi'
  | '01.circle.hi'
  | '01.square.ar'
  | '01.square.fill.ar'
  | '01.square.fill.hi'
  | '01.square.hi'
  | '02.circle.ar'
  | '02.circle.fill.ar'
  | '02.circle.fill.hi'
  | '02.circle.hi'
  | '02.square.ar'
  | '02.square.fill.ar'
  | '02.square.fill.hi'
  | '02.square.hi'
  | '03.circle.ar'
  | '03.circle.fill.ar'
  | '03.circle.fill.hi'
  | '03.circle.hi'
  | '03.square.ar'
  | '03.square.fill.ar'
  | '03.square.fill.hi'
  | '03.square.hi'
  | '04.circle.ar'
  | '04.circle.fill.ar'
  | '04.circle.fill.hi'
  | '04.circle.hi'
  | '04.square.ar'
  | '04.square.fill.ar'
  | '04.square.fill.hi'
  | '04.square.hi'
  | '05.circle.ar'
  | '05.circle.fill.ar'
  | '05.circle.fill.hi'
  | '05.circle.hi'
  | '05.square.ar'
  | '05.square.fill.ar'
  | '05.square.fill.hi'
  | '05.square.hi'
  | '06.circle.ar'
  | '06.circle.fill.ar'
  | '06.circle.fill.hi'
  | '06.circle.hi'
  | '06.square.ar'
  | '06.square.fill.ar'
  | '06.square.fill.hi'
  | '06.square.hi'
  | '07.circle.ar'
  | '07.circle.fill.ar'
  | '07.circle.fill.hi'
  | '07.circle.hi'
  | '07.square.ar'
  | '07.square.fill.ar'
  | '07.square.fill.hi'
  | '07.square.hi'
  | '08.circle.ar'
  | '08.circle.fill.ar'
  | '08.circle.fill.hi'
  | '08.circle.hi'
  | '08.square.ar'
  | '08.square.fill.ar'
  | '08.square.fill.hi'
  | '08.square.hi'
  | '09.circle.ar'
  | '09.circle.fill.ar'
  | '09.circle.fill.hi'
  | '09.circle.hi'
  | '09.square.ar'
  | '09.square.fill.ar'
  | '09.square.fill.hi'
  | '09.square.hi'
  | '1.circle.fill.hi'
  | '1.circle.hi'
  | '1.square.fill.hi'
  | '1.square.hi'
  | '10.circle.ar'
  | '10.circle.fill.ar'
  | '10.circle.fill.hi'
  | '10.circle.hi'
  | '10.square.ar'
  | '10.square.fill.ar'
  | '10.square.fill.hi'
  | '10.square.hi'
  | '11.circle.ar'
  | '11.circle.fill.ar'
  | '11.circle.fill.hi'
  | '11.circle.hi'
  | '11.square.ar'
  | '11.square.fill.ar'
  | '11.square.fill.hi'
  | '11.square.hi'
  | '12.circle.ar'
  | '12.circle.fill.ar'
  | '12.circle.fill.hi'
  | '12.circle.hi'
  | '12.square.ar'
  | '12.square.fill.ar'
  | '12.square.fill.hi'
  | '12.square.hi'
  | '13.circle.ar'
  | '13.circle.fill.ar'
  | '13.circle.fill.hi'
  | '13.circle.hi'
  | '13.square.ar'
  | '13.square.fill.ar'
  | '13.square.fill.hi'
  | '13.square.hi'
  | '14.circle.ar'
  | '14.circle.fill.ar'
  | '14.circle.fill.hi'
  | '14.circle.hi'
  | '14.square.ar'
  | '14.square.fill.ar'
  | '14.square.fill.hi'
  | '14.square.hi'
  | '15.circle.ar'
  | '15.circle.fill.ar'
  | '15.circle.fill.hi'
  | '15.circle.hi'
  | '15.square.ar'
  | '15.square.fill.ar'
  | '15.square.fill.hi'
  | '15.square.hi'
  | '16.circle.ar'
  | '16.circle.fill.ar'
  | '16.circle.fill.hi'
  | '16.circle.hi'
  | '16.square.ar'
  | '16.square.fill.ar'
  | '16.square.fill.hi'
  | '16.square.hi'
  | '17.circle.ar'
  | '17.circle.fill.ar'
  | '17.circle.fill.hi'
  | '17.circle.hi'
  | '17.square.ar'
  | '17.square.fill.ar'
  | '17.square.fill.hi'
  | '17.square.hi'
  | '18.circle.ar'
  | '18.circle.fill.ar'
  | '18.circle.fill.hi'
  | '18.circle.hi'
  | '18.square.ar'
  | '18.square.fill.ar'
  | '18.square.fill.hi'
  | '18.square.hi'
  | '19.circle.ar'
  | '19.circle.fill.ar'
  | '19.circle.fill.hi'
  | '19.circle.hi'
  | '19.square.ar'
  | '19.square.fill.ar'
  | '19.square.fill.hi'
  | '19.square.hi'
  | '2.circle.fill.hi'
  | '2.circle.hi'
  | '2.square.fill.hi'
  | '2.square.hi'
  | '20.circle.ar'
  | '20.circle.fill.ar'
  | '20.circle.fill.hi'
  | '20.circle.hi'
  | '20.square.ar'
  | '20.square.fill.ar'
  | '20.square.fill.hi'
  | '20.square.hi'
  | '21.circle.ar'
  | '21.circle.fill.ar'
  | '21.circle.fill.hi'
  | '21.circle.hi'
  | '21.square.ar'
  | '21.square.fill.ar'
  | '21.square.fill.hi'
  | '21.square.hi'
  | '22.circle.ar'
  | '22.circle.fill.ar'
  | '22.circle.fill.hi'
  | '22.circle.hi'
  | '22.square.ar'
  | '22.square.fill.ar'
  | '22.square.fill.hi'
  | '22.square.hi'
  | '23.circle.ar'
  | '23.circle.fill.ar'
  | '23.circle.fill.hi'
  | '23.circle.hi'
  | '23.square.ar'
  | '23.square.fill.ar'
  | '23.square.fill.hi'
  | '23.square.hi'
  | '24.circle.ar'
  | '24.circle.fill.ar'
  | '24.circle.fill.hi'
  | '24.circle.hi'
  | '24.square.ar'
  | '24.square.fill.ar'
  | '24.square.fill.hi'
  | '24.square.hi'
  | '25.circle.ar'
  | '25.circle.fill.ar'
  | '25.circle.fill.hi'
  | '25.circle.hi'
  | '25.square.ar'
  | '25.square.fill.ar'
  | '25.square.fill.hi'
  | '25.square.hi'
  | '26.circle.ar'
  | '26.circle.fill.ar'
  | '26.circle.fill.hi'
  | '26.circle.hi'
  | '26.square.ar'
  | '26.square.fill.ar'
  | '26.square.fill.hi'
  | '26.square.hi'
  | '27.circle.ar'
  | '27.circle.fill.ar'
  | '27.circle.fill.hi'
  | '27.circle.hi'
  | '27.square.ar'
  | '27.square.fill.ar'
  | '27.square.fill.hi'
  | '27.square.hi'
  | '28.circle.ar'
  | '28.circle.fill.ar'
  | '28.circle.fill.hi'
  | '28.circle.hi'
  | '28.square.ar'
  | '28.square.fill.ar'
  | '28.square.fill.hi'
  | '28.square.hi'
  | '29.circle.ar'
  | '29.circle.fill.ar'
  | '29.circle.fill.hi'
  | '29.circle.hi'
  | '29.square.ar'
  | '29.square.fill.ar'
  | '29.square.fill.hi'
  | '29.square.hi'
  | '3.circle.fill.hi'
  | '3.circle.hi'
  | '3.square.fill.hi'
  | '3.square.hi'
  | '30.circle.ar'
  | '30.circle.fill.ar'
  | '30.circle.fill.hi'
  | '30.circle.hi'
  | '30.square.ar'
  | '30.square.fill.ar'
  | '30.square.fill.hi'
  | '30.square.hi'
  | '31.circle.ar'
  | '31.circle.fill.ar'
  | '31.circle.fill.hi'
  | '31.circle.hi'
  | '31.square.ar'
  | '31.square.fill.ar'
  | '31.square.fill.hi'
  | '31.square.hi'
  | '32.circle.ar'
  | '32.circle.fill.ar'
  | '32.circle.fill.hi'
  | '32.circle.hi'
  | '32.square.ar'
  | '32.square.fill.ar'
  | '32.square.fill.hi'
  | '32.square.hi'
  | '33.circle.ar'
  | '33.circle.fill.ar'
  | '33.circle.fill.hi'
  | '33.circle.hi'
  | '33.square.ar'
  | '33.square.fill.ar'
  | '33.square.fill.hi'
  | '33.square.hi'
  | '34.circle.ar'
  | '34.circle.fill.ar'
  | '34.circle.fill.hi'
  | '34.circle.hi'
  | '34.square.ar'
  | '34.square.fill.ar'
  | '34.square.fill.hi'
  | '34.square.hi'
  | '35.circle.ar'
  | '35.circle.fill.ar'
  | '35.circle.fill.hi'
  | '35.circle.hi'
  | '35.square.ar'
  | '35.square.fill.ar'
  | '35.square.fill.hi'
  | '35.square.hi'
  | '36.circle.ar'
  | '36.circle.fill.ar'
  | '36.circle.fill.hi'
  | '36.circle.hi'
  | '36.square.ar'
  | '36.square.fill.ar'
  | '36.square.fill.hi'
  | '36.square.hi'
  | '37.circle.ar'
  | '37.circle.fill.ar'
  | '37.circle.fill.hi'
  | '37.circle.hi'
  | '37.square.ar'
  | '37.square.fill.ar'
  | '37.square.fill.hi'
  | '37.square.hi'
  | '38.circle.ar'
  | '38.circle.fill.ar'
  | '38.circle.fill.hi'
  | '38.circle.hi'
  | '38.square.ar'
  | '38.square.fill.ar'
  | '38.square.fill.hi'
  | '38.square.hi'
  | '39.circle.ar'
  | '39.circle.fill.ar'
  | '39.circle.fill.hi'
  | '39.circle.hi'
  | '39.square.ar'
  | '39.square.fill.ar'
  | '39.square.fill.hi'
  | '39.square.hi'
  | '4.circle.fill.hi'
  | '4.circle.hi'
  | '4.square.fill.hi'
  | '4.square.hi'
  | '40.circle.ar'
  | '40.circle.fill.ar'
  | '40.circle.fill.hi'
  | '40.circle.hi'
  | '40.square.ar'
  | '40.square.fill.ar'
  | '40.square.fill.hi'
  | '40.square.hi'
  | '41.circle.ar'
  | '41.circle.fill.ar'
  | '41.circle.fill.hi'
  | '41.circle.hi'
  | '41.square.ar'
  | '41.square.fill.ar'
  | '41.square.fill.hi'
  | '41.square.hi'
  | '42.circle.ar'
  | '42.circle.fill.ar'
  | '42.circle.fill.hi'
  | '42.circle.hi'
  | '42.square.ar'
  | '42.square.fill.ar'
  | '42.square.fill.hi'
  | '42.square.hi'
  | '43.circle.ar'
  | '43.circle.fill.ar'
  | '43.circle.fill.hi'
  | '43.circle.hi'
  | '43.square.ar'
  | '43.square.fill.ar'
  | '43.square.fill.hi'
  | '43.square.hi'
  | '44.circle.ar'
  | '44.circle.fill.ar'
  | '44.circle.fill.hi'
  | '44.circle.hi'
  | '44.square.ar'
  | '44.square.fill.ar'
  | '44.square.fill.hi'
  | '44.square.hi'
  | '45.circle.ar'
  | '45.circle.fill.ar'
  | '45.circle.fill.hi'
  | '45.circle.hi'
  | '45.square.ar'
  | '45.square.fill.ar'
  | '45.square.fill.hi'
  | '45.square.hi'
  | '46.circle.ar'
  | '46.circle.fill.ar'
  | '46.circle.fill.hi'
  | '46.circle.hi'
  | '46.square.ar'
  | '46.square.fill.ar'
  | '46.square.fill.hi'
  | '46.square.hi'
  | '47.circle.ar'
  | '47.circle.fill.ar'
  | '47.circle.fill.hi'
  | '47.circle.hi'
  | '47.square.ar'
  | '47.square.fill.ar'
  | '47.square.fill.hi'
  | '47.square.hi'
  | '48.circle.ar'
  | '48.circle.fill.ar'
  | '48.circle.fill.hi'
  | '48.circle.hi'
  | '48.square.ar'
  | '48.square.fill.ar'
  | '48.square.fill.hi'
  | '48.square.hi'
  | '49.circle.ar'
  | '49.circle.fill.ar'
  | '49.circle.fill.hi'
  | '49.circle.hi'
  | '49.square.ar'
  | '49.square.fill.ar'
  | '49.square.fill.hi'
  | '49.square.hi'
  | '5.circle.fill.hi'
  | '5.circle.hi'
  | '5.square.fill.hi'
  | '5.square.hi'
  | '50.circle.ar'
  | '50.circle.fill.ar'
  | '50.circle.fill.hi'
  | '50.circle.hi'
  | '50.square.ar'
  | '50.square.fill.ar'
  | '50.square.fill.hi'
  | '50.square.hi'
  | '6.circle.fill.hi'
  | '6.circle.hi'
  | '6.square.fill.hi'
  | '6.square.hi'
  | '7.circle.fill.hi'
  | '7.circle.hi'
  | '7.square.fill.hi'
  | '7.square.hi'
  | '8.circle.fill.hi'
  | '8.circle.hi'
  | '8.square.fill.hi'
  | '8.square.hi'
  | '9.circle.fill.hi'
  | '9.circle.hi'
  | '9.square.fill.hi'
  | '9.square.hi'
  | 'chevron.compact.backward'
  | 'chevron.compact.forward'
  | 'person.crop.square.badge.camera'
  | 'person.crop.square.badge.camera.fill'
  | 'person.crop.square.badge.video'
  | 'person.crop.square.badge.video.fill'
  | 'square.and.arrow.up.badge.clock'
  | 'square.and.arrow.up.badge.clock.fill'

/**
 * @name SF Symbols 5.2
 * @description These symbols are available on the following platforms:
 * iOS v17.4+,
 * macOS v14.4+,
 * tvOS v17.4+,
 * visionOS v1.1+,
 * watchOS v10.4+
 */
export type SFSymbols5_2 =
  | SFSymbols5_1
  | 'apple.meditate'
  | 'apple.meditate.square.stack'
  | 'apple.meditate.square.stack.fill'
  | 'apple.terminal.circle'
  | 'apple.terminal.circle.fill'
  | 'arrow.down.app.dashed'
  | 'arrow.down.app.dashed.trianglebadge.exclamationmark'
  | 'audio.jack.mono'
  | 'audio.jack.stereo'
  | 'ipad.badge.exclamationmark'
  | 'ipad.gen1.badge.exclamationmark'
  | 'ipad.gen1.landscape.badge.exclamationmark'
  | 'ipad.gen2.badge.exclamationmark'
  | 'ipad.gen2.landscape.badge.exclamationmark'
  | 'ipad.landscape.badge.exclamationmark'
  | 'iphone.badge.exclamationmark'
  | 'iphone.gen1.badge.exclamationmark'
  | 'iphone.gen2.badge.exclamationmark'
  | 'iphone.gen3.badge.exclamationmark'
  | 'medal.star'
  | 'medal.star.fill'
  | 'plus.circle.dashed'
  | 'translate'

/**
 * @name SF Symbols 5.3
 * @description These symbols are available on the following platforms:
 * iOS v17.6+,
 * macOS v14.6+,
 * tvOS v17.6+,
 * visionOS v1.3+,
 * watchOS v10.6+
 */
export type SFSymbols5_3 =
  | SFSymbols5_2
  | 'beats.pill'
  | 'beats.pill.fill'
  | 'beats.solobuds'
  | 'beats.solobuds.chargingcase'
  | 'beats.solobuds.chargingcase.fill'
  | 'beats.solobuds.left'
  | 'beats.solobuds.right'

/**
 * @name SF Symbols 6.0
 * @description These symbols are available on the following platforms:
 * iOS v18.0+,
 * macOS v15.0+,
 * tvOS v18.0+,
 * visionOS v2.0+,
 * watchOS v11.0+
 */
export type SFSymbols6_0 =
  | SFSymbols5_3
  | '10.arrow.trianglehead.clockwise'
  | '10.arrow.trianglehead.clockwise.ar'
  | '10.arrow.trianglehead.clockwise.hi'
  | '10.arrow.trianglehead.counterclockwise'
  | '10.arrow.trianglehead.counterclockwise.ar'
  | '10.arrow.trianglehead.counterclockwise.hi'
  | '15.arrow.trianglehead.clockwise'
  | '15.arrow.trianglehead.clockwise.ar'
  | '15.arrow.trianglehead.clockwise.hi'
  | '15.arrow.trianglehead.counterclockwise'
  | '15.arrow.trianglehead.counterclockwise.ar'
  | '15.arrow.trianglehead.counterclockwise.hi'
  | '30.arrow.trianglehead.clockwise'
  | '30.arrow.trianglehead.clockwise.ar'
  | '30.arrow.trianglehead.clockwise.hi'
  | '30.arrow.trianglehead.counterclockwise'
  | '30.arrow.trianglehead.counterclockwise.ar'
  | '30.arrow.trianglehead.counterclockwise.hi'
  | '45.arrow.trianglehead.clockwise'
  | '45.arrow.trianglehead.clockwise.ar'
  | '45.arrow.trianglehead.clockwise.hi'
  | '45.arrow.trianglehead.counterclockwise'
  | '45.arrow.trianglehead.counterclockwise.ar'
  | '45.arrow.trianglehead.counterclockwise.hi'
  | '5.arrow.trianglehead.clockwise'
  | '5.arrow.trianglehead.clockwise.ar'
  | '5.arrow.trianglehead.clockwise.hi'
  | '5.arrow.trianglehead.counterclockwise'
  | '5.arrow.trianglehead.counterclockwise.ar'
  | '5.arrow.trianglehead.counterclockwise.hi'
  | '60.arrow.trianglehead.clockwise'
  | '60.arrow.trianglehead.clockwise.ar'
  | '60.arrow.trianglehead.clockwise.hi'
  | '60.arrow.trianglehead.counterclockwise'
  | '60.arrow.trianglehead.counterclockwise.ar'
  | '60.arrow.trianglehead.counterclockwise.hi'
  | '75.arrow.trianglehead.clockwise'
  | '75.arrow.trianglehead.clockwise.ar'
  | '75.arrow.trianglehead.clockwise.hi'
  | '75.arrow.trianglehead.counterclockwise'
  | '75.arrow.trianglehead.counterclockwise.ar'
  | '75.arrow.trianglehead.counterclockwise.hi'
  | '90.arrow.trianglehead.clockwise'
  | '90.arrow.trianglehead.clockwise.ar'
  | '90.arrow.trianglehead.clockwise.hi'
  | '90.arrow.trianglehead.counterclockwise'
  | '90.arrow.trianglehead.counterclockwise.ar'
  | '90.arrow.trianglehead.counterclockwise.hi'
  | 'air.car.side'
  | 'air.car.side.fill'
  | 'air.convertible.side'
  | 'air.convertible.side.fill'
  | 'air.pickup.side'
  | 'air.pickup.side.fill'
  | 'air.suv.side'
  | 'air.suv.side.fill'
  | 'airplay.audio'
  | 'airplay.audio.badge.exclamationmark'
  | 'airplay.audio.circle'
  | 'airplay.audio.circle.fill'
  | 'airplay.video'
  | 'airplay.video.badge.exclamationmark'
  | 'airplay.video.circle'
  | 'airplay.video.circle.fill'
  | 'airpods.max'
  | 'airpods.pro'
  | 'airpods.pro.chargingcase.wireless'
  | 'airpods.pro.chargingcase.wireless.fill'
  | 'airpods.pro.chargingcase.wireless.radiowaves.left.and.right'
  | 'airpods.pro.chargingcase.wireless.radiowaves.left.and.right.fill'
  | 'airpods.pro.left'
  | 'airpods.pro.right'
  | 'american.football'
  | 'american.football.circle'
  | 'american.football.circle.fill'
  | 'american.football.fill'
  | 'american.football.professional'
  | 'american.football.professional.circle'
  | 'american.football.professional.circle.fill'
  | 'american.football.professional.fill'
  | 'antenna.radiowaves.left.and.right.slash.circle'
  | 'antenna.radiowaves.left.and.right.slash.circle.fill'
  | 'app.badge.clock'
  | 'app.badge.clock.fill'
  | 'append.page'
  | 'append.page.fill'
  | 'append.page.fill.rtl'
  | 'append.page.rtl'
  | 'apple.haptics.and.exclamationmark.triangle'
  | 'apple.haptics.and.music.note'
  | 'apple.haptics.and.music.note.slash'
  | 'apple.image.playground'
  | 'apple.image.playground.fill'
  | 'apple.intelligence'
  | 'apple.meditate.circle'
  | 'apple.meditate.circle.fill'
  | 'applepencil.doubletap'
  | 'applepencil.hover'
  | 'applepencil.squeeze'
  | 'applewatch.case.sizes'
  | 'arcade.stick.and.arrow.left.and.arrow.right.outward'
  | 'arrow.backward.circle.dotted'
  | 'arrow.down.backward.and.arrow.up.forward.rectangle'
  | 'arrow.down.backward.and.arrow.up.forward.rectangle.fill'
  | 'arrow.down.backward.circle.dotted'
  | 'arrow.down.document'
  | 'arrow.down.document.fill'
  | 'arrow.down.forward.and.arrow.up.backward.rectangle'
  | 'arrow.down.forward.and.arrow.up.backward.rectangle.fill'
  | 'arrow.down.forward.circle.dotted'
  | 'arrow.down.left.and.arrow.up.right.rectangle'
  | 'arrow.down.left.and.arrow.up.right.rectangle.fill'
  | 'arrow.down.left.circle.dotted'
  | 'arrow.down.right.and.arrow.up.left.rectangle'
  | 'arrow.down.right.and.arrow.up.left.rectangle.fill'
  | 'arrow.down.right.circle.dotted'
  | 'arrow.forward.circle.dotted'
  | 'arrow.left.circle.dotted'
  | 'arrow.right.circle.dotted'
  | 'arrow.right.filled.filter.arrow.right'
  | 'arrow.right.page.on.clipboard'
  | 'arrow.trianglehead.2.clockwise'
  | 'arrow.trianglehead.2.clockwise.rotate.90'
  | 'arrow.trianglehead.2.clockwise.rotate.90.camera'
  | 'arrow.trianglehead.2.clockwise.rotate.90.camera.fill'
  | 'arrow.trianglehead.2.clockwise.rotate.90.circle'
  | 'arrow.trianglehead.2.clockwise.rotate.90.circle.fill'
  | 'arrow.trianglehead.2.clockwise.rotate.90.icloud'
  | 'arrow.trianglehead.2.clockwise.rotate.90.icloud.fill'
  | 'arrow.trianglehead.2.clockwise.rotate.90.page.on.clipboard'
  | 'arrow.trianglehead.2.counterclockwise'
  | 'arrow.trianglehead.2.counterclockwise.rotate.90'
  | 'arrow.trianglehead.bottomleft.capsulepath.clockwise'
  | 'arrow.trianglehead.branch'
  | 'arrow.trianglehead.clockwise'
  | 'arrow.trianglehead.clockwise.heart'
  | 'arrow.trianglehead.clockwise.heart.fill'
  | 'arrow.trianglehead.clockwise.icloud'
  | 'arrow.trianglehead.clockwise.icloud.fill'
  | 'arrow.trianglehead.clockwise.rotate.90'
  | 'arrow.trianglehead.counterclockwise'
  | 'arrow.trianglehead.counterclockwise.icloud'
  | 'arrow.trianglehead.counterclockwise.icloud.fill'
  | 'arrow.trianglehead.counterclockwise.rotate.90'
  | 'arrow.trianglehead.left.and.right.righttriangle.left.righttriangle.right'
  | 'arrow.trianglehead.left.and.right.righttriangle.left.righttriangle.right.fill'
  | 'arrow.trianglehead.merge'
  | 'arrow.trianglehead.pull'
  | 'arrow.trianglehead.rectanglepath'
  | 'arrow.trianglehead.swap'
  | 'arrow.trianglehead.topright.capsulepath.clockwise'
  | 'arrow.trianglehead.turn.up.right.circle'
  | 'arrow.trianglehead.turn.up.right.circle.fill'
  | 'arrow.trianglehead.turn.up.right.diamond'
  | 'arrow.trianglehead.turn.up.right.diamond.fill'
  | 'arrow.trianglehead.up.and.down.righttriangle.up.righttriangle.down'
  | 'arrow.trianglehead.up.and.down.righttriangle.up.righttriangle.down.fill'
  | 'arrow.up.backward.and.arrow.down.forward.rectangle'
  | 'arrow.up.backward.and.arrow.down.forward.rectangle.fill'
  | 'arrow.up.backward.circle.dotted'
  | 'arrow.up.circle.dotted'
  | 'arrow.up.document'
  | 'arrow.up.document.fill'
  | 'arrow.up.forward.and.arrow.down.backward.rectangle'
  | 'arrow.up.forward.and.arrow.down.backward.rectangle.fill'
  | 'arrow.up.forward.circle.dotted'
  | 'arrow.up.left.and.arrow.down.right.rectangle'
  | 'arrow.up.left.and.arrow.down.right.rectangle.fill'
  | 'arrow.up.left.circle.dotted'
  | 'arrow.up.page.on.clipboard'
  | 'arrow.up.right.circle.dotted'
  | 'australian.football'
  | 'australian.football.circle'
  | 'australian.football.circle.fill'
  | 'australian.football.fill'
  | 'australiandollarsign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'australiandollarsign.bank.building'
  | 'australiandollarsign.bank.building.fill'
  | 'australiandollarsign.gauge.chart.lefthalf.righthalf'
  | 'australiandollarsign.gauge.chart.leftthird.topthird.rightthird'
  | 'australiandollarsign.ring'
  | 'australiandollarsign.ring.dashed'
  | 'australsign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'australsign.bank.building'
  | 'australsign.bank.building.fill'
  | 'australsign.gauge.chart.lefthalf.righthalf'
  | 'australsign.gauge.chart.leftthird.topthird.rightthird'
  | 'australsign.ring'
  | 'australsign.ring.dashed'
  | 'bahtsign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'bahtsign.bank.building'
  | 'bahtsign.bank.building.fill'
  | 'bahtsign.gauge.chart.lefthalf.righthalf'
  | 'bahtsign.gauge.chart.leftthird.topthird.rightthird'
  | 'bahtsign.ring'
  | 'bahtsign.ring.dashed'
  | 'base.unit'
  | 'baseball.diamond.bases.outs.indicator'
  | 'batteryblock.stack'
  | 'batteryblock.stack.badge.snowflake'
  | 'batteryblock.stack.badge.snowflake.fill'
  | 'batteryblock.stack.fill'
  | 'batteryblock.stack.trianglebadge.exclamationmark'
  | 'batteryblock.stack.trianglebadge.exclamationmark.fill'
  | 'beats.powerbeats.pro'
  | 'beats.powerbeats.pro.chargingcase'
  | 'beats.powerbeats.pro.chargingcase.fill'
  | 'beats.powerbeats.pro.left'
  | 'beats.powerbeats.pro.right'
  | 'beats.studiobuds.left'
  | 'beats.studiobuds.plus'
  | 'beats.studiobuds.plus.chargingcase'
  | 'beats.studiobuds.plus.chargingcase.fill'
  | 'beats.studiobuds.plus.left'
  | 'beats.studiobuds.plus.right'
  | 'beats.studiobuds.right'
  | 'beziercurve'
  | 'bitcoinsign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'bitcoinsign.bank.building'
  | 'bitcoinsign.bank.building.fill'
  | 'bitcoinsign.gauge.chart.lefthalf.righthalf'
  | 'bitcoinsign.gauge.chart.leftthird.topthird.rightthird'
  | 'bitcoinsign.ring'
  | 'bitcoinsign.ring.dashed'
  | 'brazilianrealsign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'brazilianrealsign.bank.building'
  | 'brazilianrealsign.bank.building.fill'
  | 'brazilianrealsign.gauge.chart.lefthalf.righthalf'
  | 'brazilianrealsign.gauge.chart.leftthird.topthird.rightthird'
  | 'brazilianrealsign.ring'
  | 'brazilianrealsign.ring.dashed'
  | 'bubble.and.pencil'
  | 'bubble.and.pencil.rtl'
  | 'calendar.and.person'
  | 'camera.macro.slash'
  | 'camera.macro.slash.circle'
  | 'camera.macro.slash.circle.fill'
  | 'capsule.on.capsule'
  | 'capsule.on.capsule.fill'
  | 'capsule.on.rectangle'
  | 'capsule.on.rectangle.fill'
  | 'car.badge.gearshape'
  | 'car.badge.gearshape.fill'
  | 'car.front.waves.left.and.right.and.up'
  | 'car.front.waves.left.and.right.and.up.fill'
  | 'car.rear.and.tire.marks.off'
  | 'car.rear.hazardsign'
  | 'car.rear.hazardsign.fill'
  | 'car.rear.road.lane.distance.1'
  | 'car.rear.road.lane.distance.1.and.gauge.open.with.lines.needle.67percent.and.arrowtriangle'
  | 'car.rear.road.lane.distance.2'
  | 'car.rear.road.lane.distance.2.and.gauge.open.with.lines.needle.67percent.and.arrowtriangle'
  | 'car.rear.road.lane.distance.3'
  | 'car.rear.road.lane.distance.3.and.gauge.open.with.lines.needle.67percent.and.arrowtriangle'
  | 'car.rear.road.lane.distance.4'
  | 'car.rear.road.lane.distance.4.and.gauge.open.with.lines.needle.67percent.and.arrowtriangle'
  | 'car.rear.road.lane.distance.5'
  | 'car.rear.road.lane.distance.5.and.gauge.open.with.lines.needle.67percent.and.arrowtriangle'
  | 'car.rear.road.lane.off'
  | 'car.rear.road.lane.wave.up'
  | 'car.rear.tilt.road.lanes.curved.right'
  | 'car.side.front.open.crop'
  | 'car.side.front.open.crop.fill'
  | 'car.side.hill.descent.control'
  | 'car.side.hill.descent.control.fill'
  | 'car.side.rear.and.exclamationmark.and.car.side.front.off'
  | 'car.side.rear.crop.trunk.partition'
  | 'car.side.rear.crop.trunk.partition.fill'
  | 'car.side.rear.open.crop'
  | 'car.side.rear.open.crop.fill'
  | 'car.side.rear.tow.hitch'
  | 'car.side.rear.tow.hitch.fill'
  | 'car.side.roof.cargo.carrier'
  | 'car.side.roof.cargo.carrier.fill'
  | 'car.side.roof.cargo.carrier.slash'
  | 'car.side.roof.cargo.carrier.slash.fill'
  | 'car.top.arrowtriangle.front.left'
  | 'car.top.arrowtriangle.front.left.fill'
  | 'car.top.arrowtriangle.front.right'
  | 'car.top.arrowtriangle.front.right.fill'
  | 'car.top.arrowtriangle.rear.left'
  | 'car.top.arrowtriangle.rear.left.fill'
  | 'car.top.arrowtriangle.rear.right'
  | 'car.top.arrowtriangle.rear.right.fill'
  | 'car.top.front.radiowaves.front.left.and.front.and.front.right'
  | 'car.top.front.radiowaves.front.left.and.front.and.front.right.fill'
  | 'car.top.radiowaves.rear.left.car.top.front'
  | 'car.top.radiowaves.rear.left.car.top.front.fill'
  | 'car.top.radiowaves.rear.right.car.top.front'
  | 'car.top.radiowaves.rear.right.car.top.front.fill'
  | 'car.top.rear.radiowaves.rear.left.and.rear.and.rear.right'
  | 'car.top.rear.radiowaves.rear.left.and.rear.and.rear.right.fill'
  | 'car.top.video.rear.left'
  | 'car.top.video.rear.left.fill'
  | 'car.top.video.rear.right'
  | 'car.top.video.rear.right.fill'
  | 'cart.badge.clock'
  | 'cart.badge.clock.fill'
  | 'cart.badge.clock.fill.rtl'
  | 'cart.badge.clock.rtl'
  | 'cedisign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'cedisign.bank.building'
  | 'cedisign.bank.building.fill'
  | 'cedisign.gauge.chart.lefthalf.righthalf'
  | 'cedisign.gauge.chart.leftthird.topthird.rightthird'
  | 'cedisign.ring'
  | 'cedisign.ring.dashed'
  | 'centsign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'centsign.bank.building'
  | 'centsign.bank.building.fill'
  | 'centsign.gauge.chart.lefthalf.righthalf'
  | 'centsign.gauge.chart.leftthird.topthird.rightthird'
  | 'centsign.ring'
  | 'centsign.ring.dashed'
  | 'character.bn'
  | 'character.circle'
  | 'character.circle.ar'
  | 'character.circle.fill'
  | 'character.circle.fill.ar'
  | 'character.circle.fill.he'
  | 'character.circle.fill.hi'
  | 'character.circle.fill.ja'
  | 'character.circle.fill.ko'
  | 'character.circle.fill.th'
  | 'character.circle.fill.zh'
  | 'character.circle.he'
  | 'character.circle.hi'
  | 'character.circle.ja'
  | 'character.circle.ko'
  | 'character.circle.th'
  | 'character.circle.zh'
  | 'character.gu'
  | 'character.kn'
  | 'character.ml'
  | 'character.mni'
  | 'character.mr'
  | 'character.or'
  | 'character.pa'
  | 'character.sat'
  | 'character.si'
  | 'character.square'
  | 'character.square.ar'
  | 'character.square.fill'
  | 'character.square.fill.ar'
  | 'character.square.fill.he'
  | 'character.square.fill.hi'
  | 'character.square.fill.ja'
  | 'character.square.fill.ko'
  | 'character.square.fill.th'
  | 'character.square.fill.zh'
  | 'character.square.he'
  | 'character.square.hi'
  | 'character.square.ja'
  | 'character.square.ko'
  | 'character.square.th'
  | 'character.square.zh'
  | 'character.ta'
  | 'character.te'
  | 'characters.lowercase'
  | 'characters.lowercase.el'
  | 'characters.lowercase.ru'
  | 'characters.uppercase'
  | 'characters.uppercase.el'
  | 'characters.uppercase.ru'
  | 'chart.bar.horizontal.page'
  | 'chart.bar.horizontal.page.fill'
  | 'chart.bar.yaxis'
  | 'chart.line.text.clipboard'
  | 'chart.line.text.clipboard.fill'
  | 'checkmark.arrow.trianglehead.counterclockwise'
  | 'checkmark.seal.text.page'
  | 'checkmark.seal.text.page.fill'
  | 'checkmark.seal.text.page.fill.rtl'
  | 'checkmark.seal.text.page.rtl'
  | 'chevron.backward.chevron.backward.dotted'
  | 'chevron.compact.left.chevron.compact.right'
  | 'chevron.compact.up.chevron.compact.down'
  | 'chevron.compact.up.chevron.compact.right.chevron.compact.down.chevron.compact.left'
  | 'chevron.down.2'
  | 'chevron.down.dotted.2'
  | 'chevron.down.forward.2'
  | 'chevron.down.forward.dotted.2'
  | 'chevron.down.right.2'
  | 'chevron.down.right.dotted.2'
  | 'chevron.forward.dotted.chevron.forward'
  | 'chevron.left.chevron.left.dotted'
  | 'chevron.left.chevron.right'
  | 'chevron.right.dotted.chevron.right'
  | 'chevron.up.2'
  | 'chevron.up.chevron.down.square'
  | 'chevron.up.chevron.down.square.fill'
  | 'chevron.up.chevron.right.chevron.down.chevron.left'
  | 'chevron.up.dotted.2'
  | 'chevron.up.forward.2'
  | 'chevron.up.forward.dotted.2'
  | 'chevron.up.right.2'
  | 'chevron.up.right.dotted.2'
  | 'chineseyuanrenminbisign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'chineseyuanrenminbisign.bank.building'
  | 'chineseyuanrenminbisign.bank.building.fill'
  | 'chineseyuanrenminbisign.gauge.chart.lefthalf.righthalf'
  | 'chineseyuanrenminbisign.gauge.chart.leftthird.topthird.rightthird'
  | 'chineseyuanrenminbisign.ring'
  | 'chineseyuanrenminbisign.ring.dashed'
  | 'circle.bottomrighthalf.pattern.checkered'
  | 'clock.arrow.trianglehead.2.counterclockwise.rotate.90'
  | 'clock.arrow.trianglehead.counterclockwise.rotate.90'
  | 'cloud.rainbow.crop'
  | 'cloud.rainbow.crop.fill'
  | 'coat'
  | 'coat.fill'
  | 'coloncurrencysign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'coloncurrencysign.bank.building'
  | 'coloncurrencysign.bank.building.fill'
  | 'coloncurrencysign.gauge.chart.lefthalf.righthalf'
  | 'coloncurrencysign.gauge.chart.leftthird.topthird.rightthird'
  | 'coloncurrencysign.ring'
  | 'coloncurrencysign.ring.dashed'
  | 'convertible.side'
  | 'convertible.side.air.circulate'
  | 'convertible.side.air.circulate.fill'
  | 'convertible.side.air.fresh'
  | 'convertible.side.air.fresh.fill'
  | 'convertible.side.and.exclamationmark'
  | 'convertible.side.and.exclamationmark.fill'
  | 'convertible.side.arrow.trianglehead.backward'
  | 'convertible.side.arrow.trianglehead.backward.fill'
  | 'convertible.side.arrow.trianglehead.forward'
  | 'convertible.side.arrow.trianglehead.forward.and.backward'
  | 'convertible.side.arrow.trianglehead.forward.and.backward.fill'
  | 'convertible.side.arrow.trianglehead.forward.fill'
  | 'convertible.side.arrowtriangle.down'
  | 'convertible.side.arrowtriangle.down.fill'
  | 'convertible.side.arrowtriangle.up'
  | 'convertible.side.arrowtriangle.up.arrowtriangle.down'
  | 'convertible.side.arrowtriangle.up.arrowtriangle.down.fill'
  | 'convertible.side.arrowtriangle.up.fill'
  | 'convertible.side.fill'
  | 'convertible.side.front.open'
  | 'convertible.side.front.open.crop'
  | 'convertible.side.front.open.crop.fill'
  | 'convertible.side.front.open.fill'
  | 'convertible.side.hill.descent.control'
  | 'convertible.side.hill.descent.control.fill'
  | 'convertible.side.hill.down'
  | 'convertible.side.hill.down.fill'
  | 'convertible.side.hill.up'
  | 'convertible.side.hill.up.fill'
  | 'convertible.side.lock'
  | 'convertible.side.lock.fill'
  | 'convertible.side.lock.open'
  | 'convertible.side.lock.open.fill'
  | 'cruzeirosign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'cruzeirosign.bank.building'
  | 'cruzeirosign.bank.building.fill'
  | 'cruzeirosign.gauge.chart.lefthalf.righthalf'
  | 'cruzeirosign.gauge.chart.leftthird.topthird.rightthird'
  | 'cruzeirosign.ring'
  | 'cruzeirosign.ring.dashed'
  | 'cup.and.heat.waves'
  | 'cup.and.heat.waves.fill'
  | 'danishkronesign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'danishkronesign.bank.building'
  | 'danishkronesign.bank.building.fill'
  | 'danishkronesign.gauge.chart.lefthalf.righthalf'
  | 'danishkronesign.gauge.chart.leftthird.topthird.rightthird'
  | 'danishkronesign.ring'
  | 'danishkronesign.ring.dashed'
  | 'degreesign.celsius'
  | 'degreesign.fahrenheit'
  | 'desktopcomputer.and.macbook'
  | 'distribute.horizontal'
  | 'distribute.horizontal.fill'
  | 'distribute.vertical'
  | 'distribute.vertical.fill'
  | 'document'
  | 'document.badge.arrow.up'
  | 'document.badge.arrow.up.fill'
  | 'document.badge.clock'
  | 'document.badge.clock.fill'
  | 'document.badge.ellipsis'
  | 'document.badge.ellipsis.fill'
  | 'document.badge.gearshape'
  | 'document.badge.gearshape.fill'
  | 'document.badge.plus'
  | 'document.badge.plus.fill'
  | 'document.circle'
  | 'document.circle.fill'
  | 'document.fill'
  | 'document.on.clipboard'
  | 'document.on.clipboard.fill'
  | 'document.on.document'
  | 'document.on.document.fill'
  | 'document.viewfinder'
  | 'document.viewfinder.fill'
  | 'dollarsign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'dollarsign.bank.building'
  | 'dollarsign.bank.building.fill'
  | 'dollarsign.gauge.chart.lefthalf.righthalf'
  | 'dollarsign.gauge.chart.leftthird.topthird.rightthird'
  | 'dollarsign.ring'
  | 'dollarsign.ring.dashed'
  | 'dongsign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'dongsign.bank.building'
  | 'dongsign.bank.building.fill'
  | 'dongsign.gauge.chart.lefthalf.righthalf'
  | 'dongsign.gauge.chart.leftthird.topthird.rightthird'
  | 'dongsign.ring'
  | 'dongsign.ring.dashed'
  | 'drone'
  | 'drone.fill'
  | 'duffle.bag'
  | 'duffle.bag.fill'
  | 'ecg.text.page'
  | 'ecg.text.page.fill'
  | 'ecg.text.page.fill.rtl'
  | 'ecg.text.page.rtl'
  | 'engine.emission.and.exclamationmark'
  | 'engine.emission.and.filter'
  | 'envelope.and.arrow.trianglehead.branch'
  | 'envelope.and.arrow.trianglehead.branch.fill'
  | 'envelope.front'
  | 'envelope.front.fill'
  | 'envelope.front.fill.rtl'
  | 'envelope.front.rtl'
  | 'eurosign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'eurosign.bank.building'
  | 'eurosign.bank.building.fill'
  | 'eurosign.gauge.chart.lefthalf.righthalf'
  | 'eurosign.gauge.chart.leftthird.topthird.rightthird'
  | 'eurosign.ring'
  | 'eurosign.ring.dashed'
  | 'eurozonesign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'eurozonesign.bank.building'
  | 'eurozonesign.bank.building.fill'
  | 'eurozonesign.gauge.chart.lefthalf.righthalf'
  | 'eurozonesign.gauge.chart.leftthird.topthird.rightthird'
  | 'eurozonesign.ring'
  | 'eurozonesign.ring.dashed'
  | 'exclamationmark.arrow.trianglehead.2.clockwise.rotate.90'
  | 'exclamationmark.arrow.trianglehead.counterclockwise.rotate.90'
  | 'figure.2.left.holdinghands'
  | 'figure.2.right.holdinghands'
  | 'figure.american.football.circle'
  | 'figure.american.football.circle.fill'
  | 'figure.archery.circle'
  | 'figure.archery.circle.fill'
  | 'figure.australian.football.circle'
  | 'figure.australian.football.circle.fill'
  | 'figure.badminton.circle'
  | 'figure.badminton.circle.fill'
  | 'figure.barre.circle'
  | 'figure.barre.circle.fill'
  | 'figure.baseball.circle'
  | 'figure.baseball.circle.fill'
  | 'figure.basketball.circle'
  | 'figure.basketball.circle.fill'
  | 'figure.bowling.circle'
  | 'figure.bowling.circle.fill'
  | 'figure.boxing.circle'
  | 'figure.boxing.circle.fill'
  | 'figure.climbing.circle'
  | 'figure.climbing.circle.fill'
  | 'figure.cooldown.circle'
  | 'figure.cooldown.circle.fill'
  | 'figure.core.training.circle'
  | 'figure.core.training.circle.fill'
  | 'figure.cricket.circle'
  | 'figure.cricket.circle.fill'
  | 'figure.cross.training.circle'
  | 'figure.cross.training.circle.fill'
  | 'figure.curling.circle'
  | 'figure.curling.circle.fill'
  | 'figure.dance.circle'
  | 'figure.dance.circle.fill'
  | 'figure.disc.sports.circle'
  | 'figure.disc.sports.circle.fill'
  | 'figure.elliptical.circle'
  | 'figure.elliptical.circle.fill'
  | 'figure.equestrian.sports.circle'
  | 'figure.equestrian.sports.circle.fill'
  | 'figure.fencing.circle'
  | 'figure.fencing.circle.fill'
  | 'figure.field.hockey'
  | 'figure.field.hockey.circle'
  | 'figure.field.hockey.circle.fill'
  | 'figure.fishing.circle'
  | 'figure.fishing.circle.fill'
  | 'figure.flexibility.circle'
  | 'figure.flexibility.circle.fill'
  | 'figure.golf.circle'
  | 'figure.golf.circle.fill'
  | 'figure.gymnastics.circle'
  | 'figure.gymnastics.circle.fill'
  | 'figure.hand.cycling.circle'
  | 'figure.hand.cycling.circle.fill'
  | 'figure.handball.circle'
  | 'figure.handball.circle.fill'
  | 'figure.highintensity.intervaltraining.circle'
  | 'figure.highintensity.intervaltraining.circle.fill'
  | 'figure.hiking.circle'
  | 'figure.hiking.circle.fill'
  | 'figure.hockey.circle'
  | 'figure.hockey.circle.fill'
  | 'figure.hunting.circle'
  | 'figure.hunting.circle.fill'
  | 'figure.ice.hockey'
  | 'figure.ice.hockey.circle'
  | 'figure.ice.hockey.circle.fill'
  | 'figure.ice.skating'
  | 'figure.ice.skating.circle'
  | 'figure.ice.skating.circle.fill'
  | 'figure.indoor.cycle.circle'
  | 'figure.indoor.cycle.circle.fill'
  | 'figure.indoor.rowing'
  | 'figure.indoor.rowing.circle'
  | 'figure.indoor.rowing.circle.fill'
  | 'figure.indoor.soccer'
  | 'figure.indoor.soccer.circle'
  | 'figure.indoor.soccer.circle.fill'
  | 'figure.jumprope.circle'
  | 'figure.jumprope.circle.fill'
  | 'figure.kickboxing.circle'
  | 'figure.kickboxing.circle.fill'
  | 'figure.lacrosse.circle'
  | 'figure.lacrosse.circle.fill'
  | 'figure.martial.arts.circle'
  | 'figure.martial.arts.circle.fill'
  | 'figure.mind.and.body.circle'
  | 'figure.mind.and.body.circle.fill'
  | 'figure.mixed.cardio.circle'
  | 'figure.mixed.cardio.circle.fill'
  | 'figure.open.water.swim.circle'
  | 'figure.open.water.swim.circle.fill'
  | 'figure.outdoor.cycle.circle'
  | 'figure.outdoor.cycle.circle.fill'
  | 'figure.outdoor.rowing'
  | 'figure.outdoor.rowing.circle'
  | 'figure.outdoor.rowing.circle.fill'
  | 'figure.outdoor.soccer'
  | 'figure.outdoor.soccer.circle'
  | 'figure.outdoor.soccer.circle.fill'
  | 'figure.pickleball.circle'
  | 'figure.pickleball.circle.fill'
  | 'figure.pilates.circle'
  | 'figure.pilates.circle.fill'
  | 'figure.play.circle'
  | 'figure.play.circle.fill'
  | 'figure.pool.swim.circle'
  | 'figure.pool.swim.circle.fill'
  | 'figure.racquetball.circle'
  | 'figure.racquetball.circle.fill'
  | 'figure.roll.circle'
  | 'figure.roll.circle.fill'
  | 'figure.roll.runningpace.circle'
  | 'figure.roll.runningpace.circle.fill'
  | 'figure.rolling.circle'
  | 'figure.rolling.circle.fill'
  | 'figure.rugby.circle'
  | 'figure.rugby.circle.fill'
  | 'figure.run.treadmill'
  | 'figure.run.treadmill.circle'
  | 'figure.run.treadmill.circle.fill'
  | 'figure.sailing.circle'
  | 'figure.sailing.circle.fill'
  | 'figure.seated.seatbelt.left.drive.seats.1'
  | 'figure.seated.seatbelt.left.drive.seats.1.1'
  | 'figure.seated.seatbelt.left.drive.seats.1.1.fill'
  | 'figure.seated.seatbelt.left.drive.seats.1.2'
  | 'figure.seated.seatbelt.left.drive.seats.1.2.fill'
  | 'figure.seated.seatbelt.left.drive.seats.1.fill'
  | 'figure.seated.seatbelt.left.drive.seats.2'
  | 'figure.seated.seatbelt.left.drive.seats.2.2'
  | 'figure.seated.seatbelt.left.drive.seats.2.2.2'
  | 'figure.seated.seatbelt.left.drive.seats.2.2.2.fill'
  | 'figure.seated.seatbelt.left.drive.seats.2.2.3'
  | 'figure.seated.seatbelt.left.drive.seats.2.2.3.fill'
  | 'figure.seated.seatbelt.left.drive.seats.2.2.fill'
  | 'figure.seated.seatbelt.left.drive.seats.2.3'
  | 'figure.seated.seatbelt.left.drive.seats.2.3.2'
  | 'figure.seated.seatbelt.left.drive.seats.2.3.2.fill'
  | 'figure.seated.seatbelt.left.drive.seats.2.3.3'
  | 'figure.seated.seatbelt.left.drive.seats.2.3.3.fill'
  | 'figure.seated.seatbelt.left.drive.seats.2.3.fill'
  | 'figure.seated.seatbelt.left.drive.seats.2.fill'
  | 'figure.seated.seatbelt.left.drive.seats.3'
  | 'figure.seated.seatbelt.left.drive.seats.3.3'
  | 'figure.seated.seatbelt.left.drive.seats.3.3.3'
  | 'figure.seated.seatbelt.left.drive.seats.3.3.3.fill'
  | 'figure.seated.seatbelt.left.drive.seats.3.3.fill'
  | 'figure.seated.seatbelt.left.drive.seats.3.fill'
  | 'figure.seated.side.left'
  | 'figure.seated.side.left.air.distribution.lower'
  | 'figure.seated.side.left.air.distribution.middle'
  | 'figure.seated.side.left.air.distribution.middle.and.lower'
  | 'figure.seated.side.left.air.distribution.middle.and.lower.angled'
  | 'figure.seated.side.left.air.distribution.upper'
  | 'figure.seated.side.left.air.distribution.upper.angled.and.lower.angled'
  | 'figure.seated.side.left.air.distribution.upper.angled.and.middle'
  | 'figure.seated.side.left.air.distribution.upper.angled.and.middle.and.lower.angled'
  | 'figure.seated.side.left.airbag.off'
  | 'figure.seated.side.left.airbag.off.2'
  | 'figure.seated.side.left.airbag.on'
  | 'figure.seated.side.left.airbag.on.2'
  | 'figure.seated.side.left.automatic'
  | 'figure.seated.side.left.fan'
  | 'figure.seated.side.left.steeringwheel'
  | 'figure.seated.side.left.windshield.front.and.heat.waves'
  | 'figure.seated.side.left.windshield.front.and.heat.waves.air.distribution.lower'
  | 'figure.seated.side.left.windshield.front.and.heat.waves.air.distribution.middle'
  | 'figure.seated.side.left.windshield.front.and.heat.waves.air.distribution.middle.and.lower'
  | 'figure.seated.side.left.windshield.front.and.heat.waves.air.distribution.upper'
  | 'figure.seated.side.left.windshield.front.and.heat.waves.air.distribution.upper.and.lower'
  | 'figure.seated.side.left.windshield.front.and.heat.waves.air.distribution.upper.and.middle'
  | 'figure.seated.side.left.windshield.front.and.heat.waves.air.distribution.upper.and.middle.and.lower'
  | 'figure.seated.side.right'
  | 'figure.seated.side.right.air.distribution.lower'
  | 'figure.seated.side.right.air.distribution.middle'
  | 'figure.seated.side.right.air.distribution.middle.and.lower'
  | 'figure.seated.side.right.air.distribution.middle.and.lower.angled'
  | 'figure.seated.side.right.air.distribution.upper'
  | 'figure.seated.side.right.air.distribution.upper.angled.and.lower.angled'
  | 'figure.seated.side.right.air.distribution.upper.angled.and.middle'
  | 'figure.seated.side.right.air.distribution.upper.angled.and.middle.and.lower.angled'
  | 'figure.seated.side.right.airbag.off'
  | 'figure.seated.side.right.airbag.off.2'
  | 'figure.seated.side.right.airbag.on'
  | 'figure.seated.side.right.airbag.on.2'
  | 'figure.seated.side.right.automatic'
  | 'figure.seated.side.right.fan'
  | 'figure.seated.side.right.steeringwheel'
  | 'figure.seated.side.right.windshield.front.and.heat.waves'
  | 'figure.seated.side.right.windshield.front.and.heat.waves.air.distribution.lower'
  | 'figure.seated.side.right.windshield.front.and.heat.waves.air.distribution.middle'
  | 'figure.seated.side.right.windshield.front.and.heat.waves.air.distribution.middle.and.lower'
  | 'figure.seated.side.right.windshield.front.and.heat.waves.air.distribution.upper'
  | 'figure.seated.side.right.windshield.front.and.heat.waves.air.distribution.upper.and.lower'
  | 'figure.seated.side.right.windshield.front.and.heat.waves.air.distribution.upper.and.middle'
  | 'figure.seated.side.right.windshield.front.and.heat.waves.air.distribution.upper.and.middle.and.lower'
  | 'figure.skateboarding'
  | 'figure.skateboarding.circle'
  | 'figure.skateboarding.circle.fill'
  | 'figure.skiing.crosscountry.circle'
  | 'figure.skiing.crosscountry.circle.fill'
  | 'figure.skiing.downhill.circle'
  | 'figure.skiing.downhill.circle.fill'
  | 'figure.snowboarding.circle'
  | 'figure.snowboarding.circle.fill'
  | 'figure.socialdance.circle'
  | 'figure.socialdance.circle.fill'
  | 'figure.softball.circle'
  | 'figure.softball.circle.fill'
  | 'figure.squash.circle'
  | 'figure.squash.circle.fill'
  | 'figure.stair.stepper.circle'
  | 'figure.stair.stepper.circle.fill'
  | 'figure.stairs.circle'
  | 'figure.stairs.circle.fill'
  | 'figure.stand.dress'
  | 'figure.stand.dress.line.vertical.figure'
  | 'figure.step.training.circle'
  | 'figure.step.training.circle.fill'
  | 'figure.strengthtraining.functional.circle'
  | 'figure.strengthtraining.functional.circle.fill'
  | 'figure.strengthtraining.traditional.circle'
  | 'figure.strengthtraining.traditional.circle.fill'
  | 'figure.surfing.circle'
  | 'figure.surfing.circle.fill'
  | 'figure.table.tennis.circle'
  | 'figure.table.tennis.circle.fill'
  | 'figure.taichi.circle'
  | 'figure.taichi.circle.fill'
  | 'figure.tennis.circle'
  | 'figure.tennis.circle.fill'
  | 'figure.track.and.field.circle'
  | 'figure.track.and.field.circle.fill'
  | 'figure.volleyball.circle'
  | 'figure.volleyball.circle.fill'
  | 'figure.walk.treadmill'
  | 'figure.walk.treadmill.circle'
  | 'figure.walk.treadmill.circle.fill'
  | 'figure.walk.triangle'
  | 'figure.walk.triangle.fill'
  | 'figure.water.fitness.circle'
  | 'figure.water.fitness.circle.fill'
  | 'figure.waterpolo.circle'
  | 'figure.waterpolo.circle.fill'
  | 'figure.wrestling.circle'
  | 'figure.wrestling.circle.fill'
  | 'figure.yoga.circle'
  | 'figure.yoga.circle.fill'
  | 'fire.extinguisher'
  | 'fire.extinguisher.fill'
  | 'flag.pattern.checkered'
  | 'flag.pattern.checkered.2.crossed'
  | 'flag.pattern.checkered.circle'
  | 'flag.pattern.checkered.circle.fill'
  | 'florinsign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'florinsign.bank.building'
  | 'florinsign.bank.building.fill'
  | 'florinsign.gauge.chart.lefthalf.righthalf'
  | 'florinsign.gauge.chart.leftthird.topthird.rightthird'
  | 'florinsign.ring'
  | 'florinsign.ring.dashed'
  | 'fluid.batteryblock'
  | 'fluid.coolant'
  | 'formfitting.gamecontroller'
  | 'formfitting.gamecontroller.fill'
  | 'francsign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'francsign.bank.building'
  | 'francsign.bank.building.fill'
  | 'francsign.gauge.chart.lefthalf.righthalf'
  | 'francsign.gauge.chart.leftthird.topthird.rightthird'
  | 'francsign.ring'
  | 'francsign.ring.dashed'
  | 'fuelpump.and.filter'
  | 'gamecontroller.circle'
  | 'gamecontroller.circle.fill'
  | 'gauge.open.with.lines.needle.33percent.and.arrow.trianglehead.from.0percent.to.50percent'
  | 'gearshape.arrow.trianglehead.2.clockwise.rotate.90'
  | 'greaterthanorequalto'
  | 'greaterthanorequalto.circle'
  | 'greaterthanorequalto.circle.fill'
  | 'greaterthanorequalto.square'
  | 'greaterthanorequalto.square.fill'
  | 'guaranisign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'guaranisign.bank.building'
  | 'guaranisign.bank.building.fill'
  | 'guaranisign.gauge.chart.lefthalf.righthalf'
  | 'guaranisign.gauge.chart.leftthird.topthird.rightthird'
  | 'guaranisign.ring'
  | 'guaranisign.ring.dashed'
  | 'hand.draw.badge.ellipsis'
  | 'hand.draw.badge.ellipsis.fill'
  | 'hand.palm.facing'
  | 'hand.palm.facing.fill'
  | 'hand.pinch'
  | 'hand.pinch.fill'
  | 'hand.point.up.braille.badge.ellipsis'
  | 'hand.point.up.braille.badge.ellipsis.fill'
  | 'hand.raised.palm.facing'
  | 'hand.raised.palm.facing.fill'
  | 'hand.rays'
  | 'hand.rays.fill'
  | 'hat.cap'
  | 'hat.cap.fill'
  | 'hat.widebrim'
  | 'hat.widebrim.fill'
  | 'head.profile.arrow.forward.and.vision.pro'
  | 'headphones.slash'
  | 'headset'
  | 'headset.circle'
  | 'headset.circle.fill'
  | 'heart.text.clipboard'
  | 'heart.text.clipboard.fill'
  | 'heat.waves.and.fan'
  | 'helmet'
  | 'helmet.fill'
  | 'hifispeaker.2.badge.minus'
  | 'hifispeaker.2.badge.minus.fill'
  | 'hifispeaker.2.badge.plus'
  | 'hifispeaker.2.badge.plus.fill'
  | 'hifispeaker.and.homepod.badge.minus'
  | 'hifispeaker.and.homepod.badge.minus.fill'
  | 'hifispeaker.and.homepod.badge.plus'
  | 'hifispeaker.and.homepod.badge.plus.fill'
  | 'hifispeaker.and.homepod.mini'
  | 'hifispeaker.and.homepod.mini.badge.minus'
  | 'hifispeaker.and.homepod.mini.badge.minus.fill'
  | 'hifispeaker.and.homepod.mini.badge.plus'
  | 'hifispeaker.and.homepod.mini.badge.plus.fill'
  | 'hifispeaker.and.homepod.mini.fill'
  | 'hifispeaker.arrow.forward'
  | 'hifispeaker.arrow.forward.fill'
  | 'hifispeaker.badge.minus'
  | 'hifispeaker.badge.minus.fill'
  | 'hifispeaker.badge.plus'
  | 'hifispeaker.badge.plus.fill'
  | 'homepod.2.badge.minus'
  | 'homepod.2.badge.minus.fill'
  | 'homepod.2.badge.plus'
  | 'homepod.2.badge.plus.fill'
  | 'homepod.and.homepod.mini'
  | 'homepod.and.homepod.mini.badge.minus'
  | 'homepod.and.homepod.mini.badge.minus.fill'
  | 'homepod.and.homepod.mini.badge.plus'
  | 'homepod.and.homepod.mini.badge.plus.fill'
  | 'homepod.and.homepod.mini.fill'
  | 'homepod.arrow.forward'
  | 'homepod.arrow.forward.fill'
  | 'homepod.badge.minus'
  | 'homepod.badge.minus.fill'
  | 'homepod.badge.plus'
  | 'homepod.badge.plus.fill'
  | 'homepod.mini'
  | 'homepod.mini.2'
  | 'homepod.mini.2.badge.minus'
  | 'homepod.mini.2.badge.minus.fill'
  | 'homepod.mini.2.badge.plus'
  | 'homepod.mini.2.badge.plus.fill'
  | 'homepod.mini.2.fill'
  | 'homepod.mini.arrow.forward'
  | 'homepod.mini.arrow.forward.fill'
  | 'homepod.mini.badge.minus'
  | 'homepod.mini.badge.minus.fill'
  | 'homepod.mini.badge.plus'
  | 'homepod.mini.badge.plus.fill'
  | 'homepod.mini.fill'
  | 'hourglass.badge.eye'
  | 'house.badge.exclamationmark'
  | 'house.badge.exclamationmark.fill'
  | 'house.badge.wifi'
  | 'house.badge.wifi.fill'
  | 'house.slash'
  | 'house.slash.fill'
  | 'hryvniasign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'hryvniasign.bank.building'
  | 'hryvniasign.bank.building.fill'
  | 'hryvniasign.gauge.chart.lefthalf.righthalf'
  | 'hryvniasign.gauge.chart.leftthird.topthird.rightthird'
  | 'hryvniasign.ring'
  | 'hryvniasign.ring.dashed'
  | 'indianrupeesign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'indianrupeesign.bank.building'
  | 'indianrupeesign.bank.building.fill'
  | 'indianrupeesign.gauge.chart.lefthalf.righthalf'
  | 'indianrupeesign.gauge.chart.leftthird.topthird.rightthird'
  | 'indianrupeesign.ring'
  | 'indianrupeesign.ring.dashed'
  | 'inhaler'
  | 'inhaler.fill'
  | 'inset.filled.applewatch.case'
  | 'inset.filled.bottomhalf.rectangle'
  | 'inset.filled.bottomhalf.rectangle.portrait'
  | 'inset.filled.bottomhalf.tophalf.rectangle'
  | 'inset.filled.bottomleading.rectangle'
  | 'inset.filled.bottomleading.rectangle.portrait'
  | 'inset.filled.bottomleft.rectangle'
  | 'inset.filled.bottomleft.rectangle.portrait'
  | 'inset.filled.bottomright.rectangle'
  | 'inset.filled.bottomright.rectangle.portrait'
  | 'inset.filled.bottomthird.rectangle'
  | 'inset.filled.bottomthird.rectangle.portrait'
  | 'inset.filled.bottomthird.square'
  | 'inset.filled.bottomtrailing.rectangle'
  | 'inset.filled.bottomtrailing.rectangle.portrait'
  | 'inset.filled.capsule'
  | 'inset.filled.capsule.portrait'
  | 'inset.filled.center.rectangle'
  | 'inset.filled.center.rectangle.badge.plus'
  | 'inset.filled.center.rectangle.portrait'
  | 'inset.filled.circle'
  | 'inset.filled.circle.dashed'
  | 'inset.filled.diamond'
  | 'inset.filled.leadinghalf.arrow.leading.rectangle'
  | 'inset.filled.leadinghalf.rectangle'
  | 'inset.filled.leadinghalf.rectangle.portrait'
  | 'inset.filled.leadinghalf.toptrailing.bottomtrailing.rectangle'
  | 'inset.filled.leadinghalf.trailinghalf.rectangle'
  | 'inset.filled.leadingthird.rectangle'
  | 'inset.filled.leadingthird.rectangle.portrait'
  | 'inset.filled.leadingthird.square'
  | 'inset.filled.lefthalf.arrow.left.rectangle'
  | 'inset.filled.lefthalf.rectangle'
  | 'inset.filled.lefthalf.rectangle.portrait'
  | 'inset.filled.lefthalf.righthalf.rectangle'
  | 'inset.filled.lefthalf.topright.bottomright.rectangle'
  | 'inset.filled.leftthird.rectangle'
  | 'inset.filled.leftthird.rectangle.portrait'
  | 'inset.filled.leftthird.square'
  | 'inset.filled.oval'
  | 'inset.filled.oval.portrait'
  | 'inset.filled.rectangle'
  | 'inset.filled.rectangle.and.cursorarrow'
  | 'inset.filled.rectangle.and.person.filled'
  | 'inset.filled.rectangle.badge.record'
  | 'inset.filled.rectangle.on.rectangle'
  | 'inset.filled.rectangle.portrait'
  | 'inset.filled.righthalf.arrow.right.rectangle'
  | 'inset.filled.righthalf.lefthalf.rectangle'
  | 'inset.filled.righthalf.rectangle'
  | 'inset.filled.righthalf.rectangle.portrait'
  | 'inset.filled.rightthird.rectangle'
  | 'inset.filled.rightthird.rectangle.portrait'
  | 'inset.filled.rightthird.square'
  | 'inset.filled.square'
  | 'inset.filled.square.dashed'
  | 'inset.filled.tophalf.bottomhalf.rectangle'
  | 'inset.filled.tophalf.bottomleft.bottomright.rectangle'
  | 'inset.filled.tophalf.rectangle'
  | 'inset.filled.tophalf.rectangle.portrait'
  | 'inset.filled.topleading.bottomleading.trailinghalf.rectangle'
  | 'inset.filled.topleading.rectangle'
  | 'inset.filled.topleading.rectangle.portrait'
  | 'inset.filled.topleft.bottomleft.righthalf.rectangle'
  | 'inset.filled.topleft.rectangle'
  | 'inset.filled.topleft.rectangle.portrait'
  | 'inset.filled.topleft.topright.bottomhalf.rectangle'
  | 'inset.filled.topleft.topright.bottomleft.bottomright.rectangle'
  | 'inset.filled.topright.rectangle'
  | 'inset.filled.topright.rectangle.portrait'
  | 'inset.filled.topthird.rectangle'
  | 'inset.filled.topthird.rectangle.portrait'
  | 'inset.filled.topthird.square'
  | 'inset.filled.toptrailing.rectangle'
  | 'inset.filled.toptrailing.rectangle.portrait'
  | 'inset.filled.trailinghalf.arrow.trailing.rectangle'
  | 'inset.filled.trailinghalf.leadinghalf.rectangle'
  | 'inset.filled.trailinghalf.rectangle'
  | 'inset.filled.trailinghalf.rectangle.portrait'
  | 'inset.filled.trailingthird.rectangle'
  | 'inset.filled.trailingthird.rectangle.portrait'
  | 'inset.filled.trailingthird.square'
  | 'inset.filled.triangle'
  | 'inset.filled.tv'
  | 'ipad.badge.location'
  | 'ipad.gen1.badge.location'
  | 'ipad.gen1.landscape.badge.location'
  | 'ipad.gen1.landscape.slash'
  | 'ipad.gen1.slash'
  | 'ipad.gen2.badge.location'
  | 'ipad.gen2.landscape.badge.location'
  | 'ipad.gen2.landscape.slash'
  | 'ipad.gen2.slash'
  | 'ipad.landscape.and.iphone'
  | 'ipad.landscape.and.iphone.slash'
  | 'ipad.landscape.badge.location'
  | 'iphone.and.arrow.forward.inward'
  | 'iphone.and.arrow.forward.outward'
  | 'iphone.and.arrow.left.and.arrow.right.inward'
  | 'iphone.and.arrow.right.inward'
  | 'iphone.and.arrow.right.outward'
  | 'iphone.app.switcher'
  | 'iphone.badge.location'
  | 'iphone.crop.circle'
  | 'iphone.dock.motorized.viewfinder'
  | 'iphone.gen1.and.arrow.left'
  | 'iphone.gen1.badge.location'
  | 'iphone.gen1.crop.circle'
  | 'iphone.gen1.landscape.slash'
  | 'iphone.gen1.motion'
  | 'iphone.gen2.and.arrow.left.and.arrow.right.inward'
  | 'iphone.gen2.badge.location'
  | 'iphone.gen2.crop.circle'
  | 'iphone.gen2.landscape.slash'
  | 'iphone.gen2.motion'
  | 'iphone.gen3.and.arrow.left.and.arrow.right.inward'
  | 'iphone.gen3.badge.location'
  | 'iphone.gen3.crop.circle'
  | 'iphone.gen3.landscape.slash'
  | 'iphone.gen3.motion'
  | 'iphone.motion'
  | 'ipod.shuffle.gen1'
  | 'ipod.shuffle.gen2'
  | 'ipod.shuffle.gen3'
  | 'ipod.shuffle.gen4'
  | 'ipod.touch'
  | 'ipod.touch.landscape'
  | 'ipod.touch.slash'
  | 'jacket'
  | 'jacket.fill'
  | 'key.2.on.ring'
  | 'key.2.on.ring.fill'
  | 'key.car.radiowaves.forward'
  | 'key.car.radiowaves.forward.fill'
  | 'key.car.radiowaves.forward.fill.rtl'
  | 'key.car.radiowaves.forward.rtl'
  | 'key.card'
  | 'key.card.fill'
  | 'kipsign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'kipsign.bank.building'
  | 'kipsign.bank.building.fill'
  | 'kipsign.gauge.chart.lefthalf.righthalf'
  | 'kipsign.gauge.chart.leftthird.topthird.rightthird'
  | 'kipsign.ring'
  | 'kipsign.ring.dashed'
  | 'ladybug.slash'
  | 'ladybug.slash.circle'
  | 'ladybug.slash.circle.fill'
  | 'ladybug.slash.fill'
  | 'larisign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'larisign.bank.building'
  | 'larisign.bank.building.fill'
  | 'larisign.gauge.chart.lefthalf.righthalf'
  | 'larisign.gauge.chart.leftthird.topthird.rightthird'
  | 'larisign.ring'
  | 'larisign.ring.dashed'
  | 'leaf.arrow.trianglehead.clockwise'
  | 'lessthanorequalto'
  | 'lessthanorequalto.circle'
  | 'lessthanorequalto.circle.fill'
  | 'lessthanorequalto.square'
  | 'lessthanorequalto.square.fill'
  | 'lirasign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'lirasign.bank.building'
  | 'lirasign.bank.building.fill'
  | 'lirasign.gauge.chart.lefthalf.righthalf'
  | 'lirasign.gauge.chart.leftthird.topthird.rightthird'
  | 'lirasign.ring'
  | 'lirasign.ring.dashed'
  | 'location.app'
  | 'location.app.fill'
  | 'lock.document'
  | 'lock.document.fill'
  | 'lock.rectangle.on.rectangle.dashed'
  | 'macbook.and.applewatch'
  | 'macbook.and.vision.pro'
  | 'macbook.slash'
  | 'malaysianringgitsign'
  | 'malaysianringgitsign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'malaysianringgitsign.bank.building'
  | 'malaysianringgitsign.bank.building.fill'
  | 'malaysianringgitsign.circle'
  | 'malaysianringgitsign.circle.fill'
  | 'malaysianringgitsign.gauge.chart.lefthalf.righthalf'
  | 'malaysianringgitsign.gauge.chart.leftthird.topthird.rightthird'
  | 'malaysianringgitsign.ring'
  | 'malaysianringgitsign.ring.dashed'
  | 'malaysianringgitsign.square'
  | 'malaysianringgitsign.square.fill'
  | 'manatsign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'manatsign.bank.building'
  | 'manatsign.bank.building.fill'
  | 'manatsign.gauge.chart.lefthalf.righthalf'
  | 'manatsign.gauge.chart.leftthird.topthird.rightthird'
  | 'manatsign.ring'
  | 'manatsign.ring.dashed'
  | 'matter.logo'
  | 'mecca'
  | 'microphone'
  | 'microphone.and.signal.meter'
  | 'microphone.and.signal.meter.fill'
  | 'microphone.badge.ellipsis'
  | 'microphone.badge.ellipsis.fill'
  | 'microphone.badge.plus'
  | 'microphone.badge.plus.fill'
  | 'microphone.badge.xmark'
  | 'microphone.badge.xmark.fill'
  | 'microphone.circle'
  | 'microphone.circle.fill'
  | 'microphone.fill'
  | 'microphone.slash'
  | 'microphone.slash.circle'
  | 'microphone.slash.circle.fill'
  | 'microphone.slash.fill'
  | 'microphone.square'
  | 'microphone.square.fill'
  | 'millsign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'millsign.bank.building'
  | 'millsign.bank.building.fill'
  | 'millsign.gauge.chart.lefthalf.righthalf'
  | 'millsign.gauge.chart.leftthird.topthird.rightthird'
  | 'millsign.ring'
  | 'millsign.ring.dashed'
  | 'minus.arrow.trianglehead.counterclockwise'
  | 'moon.road.lanes'
  | 'moped'
  | 'moped.fill'
  | 'motorcycle'
  | 'motorcycle.fill'
  | 'music.microphone'
  | 'music.microphone.circle'
  | 'music.microphone.circle.fill'
  | 'nairasign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'nairasign.bank.building'
  | 'nairasign.bank.building.fill'
  | 'nairasign.gauge.chart.lefthalf.righthalf'
  | 'nairasign.gauge.chart.leftthird.topthird.rightthird'
  | 'nairasign.ring'
  | 'nairasign.ring.dashed'
  | 'norwegiankronesign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'norwegiankronesign.bank.building'
  | 'norwegiankronesign.bank.building.fill'
  | 'norwegiankronesign.gauge.chart.lefthalf.righthalf'
  | 'norwegiankronesign.gauge.chart.leftthird.topthird.rightthird'
  | 'norwegiankronesign.ring'
  | 'norwegiankronesign.ring.dashed'
  | 'notequal'
  | 'notequal.circle'
  | 'notequal.circle.fill'
  | 'notequal.square'
  | 'notequal.square.fill'
  | 'numbers'
  | 'numbers.ar'
  | 'numbers.hi'
  | 'numbers.rectangle'
  | 'numbers.rectangle.ar'
  | 'numbers.rectangle.fill'
  | 'numbers.rectangle.fill.ar'
  | 'numbers.rectangle.fill.hi'
  | 'numbers.rectangle.hi'
  | 'oar.2.crossed.circle'
  | 'oar.2.crossed.circle.fill'
  | 'oilcan.and.thermometer'
  | 'oilcan.and.thermometer.fill'
  | 'parkingsign.radiowaves.down.right.off'
  | 'parkingsign.radiowaves.left.and.right.slash'
  | 'parkingsign.square'
  | 'parkingsign.square.fill'
  | 'person.2.arrow.trianglehead.counterclockwise'
  | 'person.2.badge.minus'
  | 'person.2.badge.minus.fill'
  | 'person.2.badge.plus'
  | 'person.2.badge.plus.fill'
  | 'person.and.arrow.left.and.arrow.right.outward'
  | 'person.badge.shield.exclamationmark'
  | 'person.badge.shield.exclamationmark.fill'
  | 'person.crop.badge.magnifyingglass'
  | 'person.crop.badge.magnifyingglass.fill'
  | 'person.crop.square.on.square.angled'
  | 'person.crop.square.on.square.angled.fill'
  | 'person.fill.and.arrow.left.and.arrow.right.outward'
  | 'personalhotspot.slash'
  | 'peruviansolessign'
  | 'peruviansolessign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'peruviansolessign.bank.building'
  | 'peruviansolessign.bank.building.fill'
  | 'peruviansolessign.circle'
  | 'peruviansolessign.circle.fill'
  | 'peruviansolessign.gauge.chart.lefthalf.righthalf'
  | 'peruviansolessign.gauge.chart.leftthird.topthird.rightthird'
  | 'peruviansolessign.ring'
  | 'peruviansolessign.ring.dashed'
  | 'peruviansolessign.square'
  | 'peruviansolessign.square.fill'
  | 'pesetasign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'pesetasign.bank.building'
  | 'pesetasign.bank.building.fill'
  | 'pesetasign.gauge.chart.lefthalf.righthalf'
  | 'pesetasign.gauge.chart.leftthird.topthird.rightthird'
  | 'pesetasign.ring'
  | 'pesetasign.ring.dashed'
  | 'pesosign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'pesosign.bank.building'
  | 'pesosign.bank.building.fill'
  | 'pesosign.gauge.chart.lefthalf.righthalf'
  | 'pesosign.gauge.chart.leftthird.topthird.rightthird'
  | 'pesosign.ring'
  | 'pesosign.ring.dashed'
  | 'phone.badge.clock'
  | 'phone.badge.clock.fill'
  | 'photo.badge.exclamationmark'
  | 'photo.badge.exclamationmark.fill'
  | 'photo.on.rectangle.angled.fill'
  | 'plus.arrow.trianglehead.clockwise'
  | 'point.bottomleft.forward.to.arrow.triangle.scurvepath'
  | 'point.bottomleft.forward.to.arrow.triangle.scurvepath.fill'
  | 'point.bottomleft.forward.to.arrow.triangle.uturn.scurvepath'
  | 'point.bottomleft.forward.to.arrow.triangle.uturn.scurvepath.fill'
  | 'point.topright.arrow.triangle.backward.to.point.bottomleft.filled.scurvepath'
  | 'point.topright.arrow.triangle.backward.to.point.bottomleft.scurvepath'
  | 'point.topright.arrow.triangle.backward.to.point.bottomleft.scurvepath.fill'
  | 'point.topright.filled.arrow.triangle.backward.to.point.bottomleft.scurvepath'
  | 'polishzlotysign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'polishzlotysign.bank.building'
  | 'polishzlotysign.bank.building.fill'
  | 'polishzlotysign.gauge.chart.lefthalf.righthalf'
  | 'polishzlotysign.gauge.chart.leftthird.topthird.rightthird'
  | 'polishzlotysign.ring'
  | 'polishzlotysign.ring.dashed'
  | 'powermeter'
  | 'powerplug.portrait'
  | 'powerplug.portrait.fill'
  | 'printer.dotmatrix.filled.and.paper.inverse'
  | 'printer.dotmatrix.inverse'
  | 'printer.filled.and.paper.inverse'
  | 'printer.inverse'
  | 'progress.indicator'
  | 'questionmark.circle.dashed'
  | 'questionmark.circle.dashed.ar'
  | 'questionmark.text.page'
  | 'questionmark.text.page.ar'
  | 'questionmark.text.page.fill'
  | 'questionmark.text.page.fill.ar'
  | 'questionmark.text.page.fill.rtl'
  | 'questionmark.text.page.rtl'
  | 'rectangle.expand.diagonal'
  | 'rectangle.grid.3x3'
  | 'rectangle.grid.3x3.fill'
  | 'rectangle.on.rectangle.dashed'
  | 'rectangle.pattern.checkered'
  | 'richtext.page'
  | 'richtext.page.ar'
  | 'richtext.page.fill'
  | 'richtext.page.fill.ar'
  | 'richtext.page.fill.he'
  | 'richtext.page.fill.hi'
  | 'richtext.page.fill.ja'
  | 'richtext.page.fill.ko'
  | 'richtext.page.fill.th'
  | 'richtext.page.fill.zh'
  | 'richtext.page.he'
  | 'richtext.page.hi'
  | 'richtext.page.ja'
  | 'richtext.page.ko'
  | 'richtext.page.th'
  | 'richtext.page.zh'
  | 'robotic.vacuum'
  | 'robotic.vacuum.fill'
  | 'rublesign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'rublesign.bank.building'
  | 'rublesign.bank.building.fill'
  | 'rublesign.gauge.chart.lefthalf.righthalf'
  | 'rublesign.gauge.chart.leftthird.topthird.rightthird'
  | 'rublesign.ring'
  | 'rublesign.ring.dashed'
  | 'rugbyball'
  | 'rugbyball.circle'
  | 'rugbyball.circle.fill'
  | 'rugbyball.fill'
  | 'rupeesign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'rupeesign.bank.building'
  | 'rupeesign.bank.building.fill'
  | 'rupeesign.gauge.chart.lefthalf.righthalf'
  | 'rupeesign.gauge.chart.leftthird.topthird.rightthird'
  | 'rupeesign.ring'
  | 'rupeesign.ring.dashed'
  | 'sharedwithyou'
  | 'sharedwithyou.circle'
  | 'sharedwithyou.circle.fill'
  | 'sharedwithyou.slash'
  | 'shekelsign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'shekelsign.bank.building'
  | 'shekelsign.bank.building.fill'
  | 'shekelsign.gauge.chart.lefthalf.righthalf'
  | 'shekelsign.gauge.chart.leftthird.topthird.rightthird'
  | 'shekelsign.ring'
  | 'shekelsign.ring.dashed'
  | 'shield.pattern.checkered'
  | 'singaporedollarsign'
  | 'singaporedollarsign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'singaporedollarsign.bank.building'
  | 'singaporedollarsign.bank.building.fill'
  | 'singaporedollarsign.circle'
  | 'singaporedollarsign.circle.fill'
  | 'singaporedollarsign.gauge.chart.lefthalf.righthalf'
  | 'singaporedollarsign.gauge.chart.leftthird.topthird.rightthird'
  | 'singaporedollarsign.ring'
  | 'singaporedollarsign.ring.dashed'
  | 'singaporedollarsign.square'
  | 'singaporedollarsign.square.fill'
  | 'slider.horizontal.2.arrow.trianglehead.counterclockwise'
  | 'slider.horizontal.2.rectangle.and.arrow.trianglehead.2.clockwise.rotate.90'
  | 'speaker.wave.1.arrowtriangles.up.right.down.left'
  | 'square.and.arrow.down.badge.clock'
  | 'square.and.arrow.down.badge.clock.fill'
  | 'square.and.arrow.up.trianglebadge.exclamationmark.fill'
  | 'square.grid.3x3.square.badge.ellipsis'
  | 'squareroot'
  | 'steeringwheel.and.hands'
  | 'steeringwheel.arrow.trianglehead.counterclockwise.and.clockwise'
  | 'sterlingsign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'sterlingsign.bank.building'
  | 'sterlingsign.bank.building.fill'
  | 'sterlingsign.gauge.chart.lefthalf.righthalf'
  | 'sterlingsign.gauge.chart.leftthird.topthird.rightthird'
  | 'sterlingsign.ring'
  | 'sterlingsign.ring.dashed'
  | 'sun.lefthalf.filled'
  | 'sun.righthalf.filled'
  | 'suspension.shock'
  | 'suv.side.front.open.crop'
  | 'suv.side.front.open.crop.fill'
  | 'suv.side.hill.descent.control'
  | 'suv.side.hill.descent.control.fill'
  | 'suv.side.rear.open.crop'
  | 'suv.side.rear.open.crop.fill'
  | 'suv.side.roof.cargo.carrier'
  | 'suv.side.roof.cargo.carrier.fill'
  | 'suv.side.roof.cargo.carrier.slash'
  | 'suv.side.roof.cargo.carrier.slash.fill'
  | 'swedishkronasign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'swedishkronasign.bank.building'
  | 'swedishkronasign.bank.building.fill'
  | 'swedishkronasign.gauge.chart.lefthalf.righthalf'
  | 'swedishkronasign.gauge.chart.leftthird.topthird.rightthird'
  | 'swedishkronasign.ring'
  | 'swedishkronasign.ring.dashed'
  | 'tachometer'
  | 'tengesign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'tengesign.bank.building'
  | 'tengesign.bank.building.fill'
  | 'tengesign.gauge.chart.lefthalf.righthalf'
  | 'tengesign.gauge.chart.leftthird.topthird.rightthird'
  | 'tengesign.ring'
  | 'tengesign.ring.dashed'
  | 'text.bubble.badge.clock'
  | 'text.bubble.badge.clock.fill'
  | 'text.bubble.badge.clock.fill.rtl'
  | 'text.bubble.badge.clock.rtl'
  | 'text.document'
  | 'text.document.fill'
  | 'text.line.magnify'
  | 'text.page'
  | 'text.page.badge.magnifyingglass'
  | 'text.page.fill'
  | 'text.page.slash'
  | 'text.page.slash.fill'
  | 'text.page.slash.fill.rtl'
  | 'text.page.slash.rtl'
  | 'text.rectangle.page'
  | 'text.rectangle.page.fill'
  | 'textformat.alt.ar'
  | 'textformat.alt.el'
  | 'textformat.alt.he'
  | 'textformat.alt.hi'
  | 'textformat.alt.ja'
  | 'textformat.alt.ko'
  | 'textformat.alt.th'
  | 'textformat.alt.zh'
  | 'textformat.ar'
  | 'textformat.characters'
  | 'textformat.characters.ar'
  | 'textformat.characters.arrow.left.and.right'
  | 'textformat.characters.arrow.left.and.right.ar'
  | 'textformat.characters.arrow.left.and.right.el'
  | 'textformat.characters.arrow.left.and.right.he'
  | 'textformat.characters.arrow.left.and.right.hi'
  | 'textformat.characters.arrow.left.and.right.ja'
  | 'textformat.characters.arrow.left.and.right.ko'
  | 'textformat.characters.arrow.left.and.right.ru'
  | 'textformat.characters.arrow.left.and.right.th'
  | 'textformat.characters.arrow.left.and.right.zh'
  | 'textformat.characters.dottedunderline'
  | 'textformat.characters.dottedunderline.ar'
  | 'textformat.characters.dottedunderline.el'
  | 'textformat.characters.dottedunderline.he'
  | 'textformat.characters.dottedunderline.hi'
  | 'textformat.characters.dottedunderline.ja'
  | 'textformat.characters.dottedunderline.ko'
  | 'textformat.characters.dottedunderline.ru'
  | 'textformat.characters.dottedunderline.th'
  | 'textformat.characters.dottedunderline.zh'
  | 'textformat.characters.el'
  | 'textformat.characters.he'
  | 'textformat.characters.hi'
  | 'textformat.characters.ja'
  | 'textformat.characters.ko'
  | 'textformat.characters.ru'
  | 'textformat.characters.th'
  | 'textformat.characters.zh'
  | 'textformat.el'
  | 'textformat.he'
  | 'textformat.hi'
  | 'textformat.ja'
  | 'textformat.ko'
  | 'textformat.numbers'
  | 'textformat.numbers.ar'
  | 'textformat.numbers.bn'
  | 'textformat.numbers.gu'
  | 'textformat.numbers.hi'
  | 'textformat.numbers.km'
  | 'textformat.numbers.kn'
  | 'textformat.numbers.ml'
  | 'textformat.numbers.mni'
  | 'textformat.numbers.my'
  | 'textformat.numbers.or'
  | 'textformat.numbers.pa'
  | 'textformat.numbers.sat'
  | 'textformat.numbers.te'
  | 'textformat.size.bn'
  | 'textformat.size.gu'
  | 'textformat.size.kn'
  | 'textformat.size.larger.bn'
  | 'textformat.size.larger.gu'
  | 'textformat.size.larger.kn'
  | 'textformat.size.larger.ml'
  | 'textformat.size.larger.mni'
  | 'textformat.size.larger.mr'
  | 'textformat.size.larger.or'
  | 'textformat.size.larger.pa'
  | 'textformat.size.larger.sat'
  | 'textformat.size.larger.si'
  | 'textformat.size.larger.ta'
  | 'textformat.size.larger.te'
  | 'textformat.size.ml'
  | 'textformat.size.mni'
  | 'textformat.size.mr'
  | 'textformat.size.or'
  | 'textformat.size.pa'
  | 'textformat.size.sat'
  | 'textformat.size.si'
  | 'textformat.size.smaller.bn'
  | 'textformat.size.smaller.gu'
  | 'textformat.size.smaller.kn'
  | 'textformat.size.smaller.ml'
  | 'textformat.size.smaller.mni'
  | 'textformat.size.smaller.mr'
  | 'textformat.size.smaller.or'
  | 'textformat.size.smaller.pa'
  | 'textformat.size.smaller.sat'
  | 'textformat.size.smaller.si'
  | 'textformat.size.smaller.ta'
  | 'textformat.size.smaller.te'
  | 'textformat.size.ta'
  | 'textformat.size.te'
  | 'textformat.th'
  | 'textformat.zh'
  | 'thermometer.and.liquid.waves.snowflake'
  | 'thermometer.and.liquid.waves.trianglebadge.exclamationmark'
  | 'thermometer.variable'
  | 'tire'
  | 'tire.badge.snowflake'
  | 'tow.hitch'
  | 'tow.hitch.exclamationmark'
  | 'tow.hitch.exclamationmark.fill'
  | 'tow.hitch.fill'
  | 'truck.pickup.side.front.open.crop'
  | 'truck.pickup.side.front.open.crop.fill'
  | 'truck.side.hill.descent.control'
  | 'truck.side.hill.descent.control.fill'
  | 'truck.side.roof.cargo.carrier'
  | 'truck.side.roof.cargo.carrier.fill'
  | 'truck.side.roof.cargo.carrier.slash'
  | 'truck.side.roof.cargo.carrier.slash.fill'
  | 'tsa'
  | 'tsa.circle'
  | 'tsa.circle.fill'
  | 'tsa.slash'
  | 'tugriksign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'tugriksign.bank.building'
  | 'tugriksign.bank.building.fill'
  | 'tugriksign.gauge.chart.lefthalf.righthalf'
  | 'tugriksign.gauge.chart.leftthird.topthird.rightthird'
  | 'tugriksign.ring'
  | 'tugriksign.ring.dashed'
  | 'turkishlirasign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'turkishlirasign.bank.building'
  | 'turkishlirasign.bank.building.fill'
  | 'turkishlirasign.gauge.chart.lefthalf.righthalf'
  | 'turkishlirasign.gauge.chart.leftthird.topthird.rightthird'
  | 'turkishlirasign.ring'
  | 'turkishlirasign.ring.dashed'
  | 'vision.pro'
  | 'vision.pro.and.arrow.forward'
  | 'vision.pro.and.arrow.forward.fill'
  | 'vision.pro.badge.exclamationmark'
  | 'vision.pro.badge.exclamationmark.fill'
  | 'vision.pro.badge.play'
  | 'vision.pro.badge.play.fill'
  | 'vision.pro.circle'
  | 'vision.pro.circle.fill'
  | 'vision.pro.fill'
  | 'vision.pro.slash'
  | 'vision.pro.slash.circle'
  | 'vision.pro.slash.circle.fill'
  | 'vision.pro.slash.fill'
  | 'vision.pro.trianglebadge.exclamationmark'
  | 'vision.pro.trianglebadge.exclamationmark.fill'
  | 'wallet.bifold'
  | 'wallet.bifold.fill'
  | 'wand.and.sparkles'
  | 'wand.and.sparkles.inverse'
  | 'water.waves.and.arrow.trianglehead.down'
  | 'water.waves.and.arrow.trianglehead.down.trianglebadge.exclamationmark'
  | 'water.waves.and.arrow.trianglehead.up'
  | 'wave.3.down'
  | 'wave.3.down.car.side'
  | 'wave.3.down.car.side.fill'
  | 'wave.3.down.circle'
  | 'wave.3.down.circle.fill'
  | 'wave.3.down.convertible.side'
  | 'wave.3.down.convertible.side.fill'
  | 'wave.3.down.pickup.side'
  | 'wave.3.down.pickup.side.fill'
  | 'wave.3.down.suv.side'
  | 'wave.3.down.suv.side.fill'
  | 'wave.3.up'
  | 'wave.3.up.circle'
  | 'wave.3.up.circle.fill'
  | 'waveform.badge.microphone'
  | 'wheelchair'
  | 'widget.extralarge'
  | 'widget.extralarge.badge.plus'
  | 'widget.large'
  | 'widget.large.badge.plus'
  | 'widget.medium'
  | 'widget.medium.badge.plus'
  | 'widget.small'
  | 'widget.small.badge.plus'
  | 'wonsign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'wonsign.bank.building'
  | 'wonsign.bank.building.fill'
  | 'wonsign.gauge.chart.lefthalf.righthalf'
  | 'wonsign.gauge.chart.leftthird.topthird.rightthird'
  | 'wonsign.ring'
  | 'wonsign.ring.dashed'
  | 'xmark.triangle.circle.square'
  | 'xmark.triangle.circle.square.fill'
  | 'yensign.arrow.trianglehead.counterclockwise.rotate.90'
  | 'yensign.bank.building'
  | 'yensign.bank.building.fill'
  | 'yensign.gauge.chart.lefthalf.righthalf'
  | 'yensign.gauge.chart.leftthird.topthird.rightthird'
  | 'yensign.ring'
  | 'yensign.ring.dashed'
  | 'zipper.page'

export type SFSymbol =
  Overrides extends { SFSymbolsVersion: '1.0' } ? SFSymbols1_0 :
  Overrides extends { SFSymbolsVersion: '1.1' } ? SFSymbols1_1 :
  Overrides extends { SFSymbolsVersion: '2.0' } ? SFSymbols2_0 :
  Overrides extends { SFSymbolsVersion: '2.1' } ? SFSymbols2_1 :
  Overrides extends { SFSymbolsVersion: '2.2' } ? SFSymbols2_2 :
  Overrides extends { SFSymbolsVersion: '3.0' } ? SFSymbols3_0 :
  Overrides extends { SFSymbolsVersion: '3.1' } ? SFSymbols3_1 :
  Overrides extends { SFSymbolsVersion: '3.2' } ? SFSymbols3_2 :
  Overrides extends { SFSymbolsVersion: '3.3' } ? SFSymbols3_3 :
  Overrides extends { SFSymbolsVersion: '4.0' } ? SFSymbols4_0 :
  Overrides extends { SFSymbolsVersion: '4.1' } ? SFSymbols4_1 :
  Overrides extends { SFSymbolsVersion: '4.2' } ? SFSymbols4_2 :
  Overrides extends { SFSymbolsVersion: '5.0' } ? SFSymbols5_0 :
  Overrides extends { SFSymbolsVersion: '5.1' } ? SFSymbols5_1 :
  Overrides extends { SFSymbolsVersion: '5.2' } ? SFSymbols5_2 :
  Overrides extends { SFSymbolsVersion: '5.3' } ? SFSymbols5_3 :
  Overrides extends { SFSymbolsVersion: '6.0' } ? SFSymbols6_0 :
  SFSymbols6_0
